#!/usr/bin/env python3
"""
Comprehensive Test Suite for Maker-Only Trade_BTC.py
Tests all scenarios without executing real trades
"""

import sys
import os
import time
import unittest
from unittest.mock import Mock, patch, MagicMock
import pandas as pd

# Add the fund_bybit directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

# Import the trading module
import Trade_BTC

class TestMakerOnlyTrading(unittest.TestCase):
    """Test suite for maker-only trading functionality"""
    
    def setUp(self):
        """Set up test environment"""
        # Mock the global variables
        Trade_BTC.EXCHANGE = Mock()
        Trade_BTC.SYMBOL = "BTCUSDT"
        Trade_BTC.MIN_POS = 0.001
        Trade_BTC.selected_exchange = "BYBIT"
        Trade_BTC.keys = {'VIP': '0', 'API_KEY': 'test', 'SECRET_KEY': 'test'}
        
        # Mock successful order response
        self.mock_order_response = {
            'id': 'test_order_123',
            'symbol': 'BTCUSDT',
            'side': 'buy',
            'amount': 0.1,
            'price': 43250.0,
            'filled': 0.1,
            'status': 'closed',
            'fee': {'cost': 0.5, 'currency': 'USDT'},
            'average': 43250.0
        }
        
        # Mock order book
        self.mock_order_book = {
            'bids': [[43245.0, 1.5], [43244.0, 2.0]],
            'asks': [[43255.0, 1.2], [43256.0, 1.8]]
        }
        
        # Mock ticker
        self.mock_ticker = {'last': 43250.0}

    def test_order_classification_small_order(self):
        """Test small order classification"""
        print("\n🧪 TEST 1: Small Order Classification")
        
        # Mock ticker for price calculation
        Trade_BTC.EXCHANGE.fetch_ticker.return_value = self.mock_ticker
        
        # Test small order by quantity
        is_large, usd_value, reason = Trade_BTC.classify_order_size("BTCUSDT", 0.5)
        self.assertFalse(is_large)
        self.assertAlmostEqual(usd_value, 21625.0, places=1)
        print(f"✅ Small order by quantity: {reason}")
        
        # Test small order by USD value
        is_large, usd_value, reason = Trade_BTC.classify_order_size("BTCUSDT", 0.1)
        self.assertFalse(is_large)
        self.assertLess(usd_value, Trade_BTC.LARGE_ORDER_USD_THRESHOLD)
        print(f"✅ Small order by USD value: {reason}")

    def test_order_classification_large_order(self):
        """Test large order classification"""
        print("\n🧪 TEST 2: Large Order Classification")
        
        Trade_BTC.EXCHANGE.fetch_ticker.return_value = self.mock_ticker
        
        # Test large order by quantity
        is_large, usd_value, reason = Trade_BTC.classify_order_size("BTCUSDT", 1.5)
        self.assertTrue(is_large)
        print(f"✅ Large order by quantity: {reason}")
        
        # Test large order by USD value (if price were higher)
        mock_high_price_ticker = {'last': 50000.0}
        Trade_BTC.EXCHANGE.fetch_ticker.return_value = mock_high_price_ticker
        is_large, usd_value, reason = Trade_BTC.classify_order_size("BTCUSDT", 0.25)
        self.assertTrue(is_large)
        self.assertGreaterEqual(usd_value, Trade_BTC.LARGE_ORDER_USD_THRESHOLD)
        print(f"✅ Large order by USD value: {reason}")

    def test_maker_only_order_execution(self):
        """Test that all orders use postOnly=True"""
        print("\n🧪 TEST 3: Maker-Only Order Execution")
        
        # Mock successful order execution
        Trade_BTC.EXCHANGE.fetch_order_book.return_value = self.mock_order_book
        Trade_BTC.EXCHANGE.create_order.return_value = self.mock_order_response
        Trade_BTC.EXCHANGE.fetch_open_order.return_value = self.mock_order_response
        
        # Test small order execution
        with patch.object(Trade_BTC, 'ensure_exchange_credentials'):
            result = Trade_BTC.execute_order_with_params(
                symbol="BTCUSDT",
                side="buy",
                quantity=0.1,
                timeout_seconds=15,
                spread_percentage=0.00005,
                check_interval=2,
                max_attempts=3,
                order_type="test"
            )
        
        # Verify postOnly was used
        call_args = Trade_BTC.EXCHANGE.create_order.call_args
        self.assertIsNotNone(call_args)
        params = call_args[1]['params']
        self.assertTrue(params.get('postOnly'))
        print("✅ Confirmed postOnly=True in order params")
        
        # Verify no market orders were attempted
        for call in Trade_BTC.EXCHANGE.create_order.call_args_list:
            order_type = call[1]['type']
            self.assertEqual(order_type, 'limit')
        print("✅ Confirmed only limit orders were used")

    def test_infinite_retry_logic(self):
        """Test infinite retry mode for unfilled orders"""
        print("\n🧪 TEST 4: Infinite Retry Logic")
        
        # Mock partial fill scenario
        partial_fill_response = self.mock_order_response.copy()
        partial_fill_response['filled'] = 0.05  # Only half filled
        
        Trade_BTC.EXCHANGE.fetch_order_book.return_value = self.mock_order_book
        Trade_BTC.EXCHANGE.create_order.return_value = partial_fill_response
        Trade_BTC.EXCHANGE.fetch_open_order.return_value = partial_fill_response
        Trade_BTC.EXCHANGE.cancel_order.return_value = True
        
        # Test with infinite retry disabled (should stop after max attempts)
        original_infinite_retry = Trade_BTC.INFINITE_RETRY_MODE
        Trade_BTC.INFINITE_RETRY_MODE = False
        
        with patch.object(Trade_BTC, 'ensure_exchange_credentials'):
            with patch('time.sleep'):  # Speed up test
                result = Trade_BTC.execute_order_with_params(
                    symbol="BTCUSDT",
                    side="buy", 
                    quantity=0.1,
                    timeout_seconds=5,
                    spread_percentage=0.00005,
                    check_interval=1,
                    max_attempts=2,
                    order_type="test"
                )
        
        # Should return partial result when infinite retry is disabled
        self.assertIsNotNone(result)
        print("✅ Infinite retry disabled: returned partial result after max attempts")
        
        # Restore original setting
        Trade_BTC.INFINITE_RETRY_MODE = original_infinite_retry

    def test_small_order_execution_path(self):
        """Test complete small order execution path"""
        print("\n🧪 TEST 5: Small Order Execution Path")
        
        Trade_BTC.EXCHANGE.fetch_ticker.return_value = self.mock_ticker
        Trade_BTC.EXCHANGE.fetch_order_book.return_value = self.mock_order_book
        Trade_BTC.EXCHANGE.create_order.return_value = self.mock_order_response
        Trade_BTC.EXCHANGE.fetch_open_order.return_value = self.mock_order_response
        
        with patch.object(Trade_BTC, 'ensure_exchange_credentials'):
            with patch.object(Trade_BTC, 'calculate_order_metrics') as mock_metrics:
                with patch.object(Trade_BTC, 'send_order_completion_telegram') as mock_telegram:
                    mock_metrics.return_value = {
                        'total_filled': 0.1,
                        'slippage_bps': -0.5,
                        'fee_rate_bps': -2.5,
                        'chunk_count': 1
                    }
                    
                    with patch('time.sleep'):  # Speed up test
                        result = Trade_BTC.execute_small_order("BTCUSDT", "buy", 0.1)
        
        self.assertIsNotNone(result)
        self.assertEqual(result['id'], 'test_order_123')
        
        # Verify metrics and telegram notification were called
        mock_metrics.assert_called_once()
        mock_telegram.assert_called_once()
        print("✅ Small order execution completed with metrics and notification")

    def test_large_order_single_execution(self):
        """Test large order execution without chunking"""
        print("\n🧪 TEST 6: Large Order Single Execution")
        
        Trade_BTC.EXCHANGE.fetch_ticker.return_value = self.mock_ticker
        Trade_BTC.EXCHANGE.fetch_order_book.return_value = self.mock_order_book
        Trade_BTC.EXCHANGE.create_order.return_value = self.mock_order_response
        Trade_BTC.EXCHANGE.fetch_open_order.return_value = self.mock_order_response
        
        with patch.object(Trade_BTC, 'ensure_exchange_credentials'):
            with patch.object(Trade_BTC, 'calculate_order_metrics') as mock_metrics:
                with patch.object(Trade_BTC, 'send_order_completion_telegram') as mock_telegram:
                    mock_metrics.return_value = {
                        'total_filled': 0.8,
                        'slippage_bps': 1.2,
                        'fee_rate_bps': -2.5,
                        'chunk_count': 1
                    }
                    
                    with patch('time.sleep'):
                        # Test large order that doesn't need chunking (0.8 < 2 * CHUNK_SIZE)
                        result = Trade_BTC.execute_large_order("BTCUSDT", "buy", 0.8)
        
        self.assertIsNotNone(result)
        mock_metrics.assert_called_once()
        mock_telegram.assert_called_once()
        print("✅ Large order single execution completed")

    def test_chunked_order_multiprocessing(self):
        """Test chunked order execution with multiprocessing"""
        print("\n🧪 TEST 7: Chunked Order Multiprocessing")
        
        # Mock successful chunk execution
        mock_chunk_result = {
            'chunk_id': 1,
            'success': True,
            'result': self.mock_order_response,
            'filled': 0.5,
            'error': None
        }
        
        with patch.object(Trade_BTC, 'execute_single_chunk_process') as mock_chunk_process:
            with patch('concurrent.futures.ProcessPoolExecutor') as mock_executor:
                with patch.object(Trade_BTC, 'calculate_order_metrics') as mock_metrics:
                    with patch.object(Trade_BTC, 'send_order_completion_telegram') as mock_telegram:
                        
                        # Mock ProcessPoolExecutor behavior
                        mock_future = Mock()
                        mock_future.result.return_value = mock_chunk_result
                        mock_executor.return_value.__enter__.return_value.submit.return_value = mock_future
                        
                        # Mock as_completed to return the future immediately
                        with patch('concurrent.futures.as_completed') as mock_as_completed:
                            mock_as_completed.return_value = [mock_future]
                            
                            mock_metrics.return_value = {
                                'total_filled': 2.5,
                                'slippage_bps': 0.8,
                                'fee_rate_bps': -2.5,
                                'chunk_count': 5
                            }
                            
                            with patch('time.sleep'):
                                result = Trade_BTC.execute_chunked_order("BTCUSDT", "buy", 2.5)
        
        self.assertIsNotNone(result)
        mock_metrics.assert_called_once()
        mock_telegram.assert_called_once()
        print("✅ Chunked order multiprocessing simulation completed")

    def test_aggressive_limit_order_classification(self):
        """Test the main aggressive_limit_order function classification"""
        print("\n🧪 TEST 8: Aggressive Limit Order Classification")
        
        Trade_BTC.EXCHANGE.fetch_ticker.return_value = self.mock_ticker
        
        with patch.object(Trade_BTC, 'execute_small_order') as mock_small:
            with patch.object(Trade_BTC, 'execute_large_order') as mock_large:
                mock_small.return_value = self.mock_order_response
                mock_large.return_value = self.mock_order_response
                
                # Test small order routing
                result1 = Trade_BTC.aggressive_limit_order("BTCUSDT", "buy", 0.3)
                mock_small.assert_called_once()
                print("✅ Small order routed to execute_small_order")
                
                # Test large order routing
                result2 = Trade_BTC.aggressive_limit_order("BTCUSDT", "buy", 1.5)
                mock_large.assert_called_once()
                print("✅ Large order routed to execute_large_order")

    def test_error_handling_scenarios(self):
        """Test various error handling scenarios"""
        print("\n🧪 TEST 9: Error Handling Scenarios")
        
        # Test exchange connection error
        Trade_BTC.EXCHANGE.fetch_order_book.side_effect = Exception("Connection timeout")
        
        with patch.object(Trade_BTC, 'ensure_exchange_credentials'):
            with patch.object(Trade_BTC, 'categorize_trading_error') as mock_categorize:
                mock_categorize.return_value = (False, "Network Error", "Retryable network issue")
                
                # Test with infinite retry disabled to avoid infinite loop
                original_infinite_retry = Trade_BTC.INFINITE_RETRY_MODE
                Trade_BTC.INFINITE_RETRY_MODE = False
                
                with patch('time.sleep'):
                    result = Trade_BTC.execute_order_with_params(
                        symbol="BTCUSDT",
                        side="buy",
                        quantity=0.1,
                        timeout_seconds=5,
                        spread_percentage=0.00005,
                        check_interval=1,
                        max_attempts=2,
                        order_type="test"
                    )
                
                # Restore settings
                Trade_BTC.INFINITE_RETRY_MODE = original_infinite_retry
                Trade_BTC.EXCHANGE.fetch_order_book.side_effect = None
                
                print("✅ Error handling completed without crashing")

    def test_metrics_calculation(self):
        """Test order metrics calculation"""
        print("\n🧪 TEST 10: Metrics Calculation")
        
        # Create multiple order results for testing
        order_results = [
            {
                'filled': 0.5,
                'average': 43250.0,
                'fee': {'cost': 1.0},
                'price': 43250.0
            },
            {
                'filled': 0.3,
                'average': 43255.0,
                'fee': {'cost': 0.6},
                'price': 43255.0
            }
        ]
        
        Trade_BTC.EXCHANGE.fetch_ticker.return_value = self.mock_ticker
        
        metrics = Trade_BTC.calculate_order_metrics(
            order_results, "BTCUSDT", 1.0, "buy"
        )
        
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics['total_filled'], 0.8)
        self.assertEqual(metrics['chunk_count'], 2)
        self.assertIn('slippage_bps', metrics)
        self.assertIn('fee_rate_bps', metrics)
        print(f"✅ Metrics calculated: {metrics['total_filled']} filled across {metrics['chunk_count']} chunks")

    def test_telegram_notification(self):
        """Test Telegram notification formatting"""
        print("\n🧪 TEST 11: Telegram Notification")
        
        test_metrics = {
            'total_filled': 1.5,
            'avg_execution_price': 43252.5,
            'current_market_price': 43250.0,
            'slippage_bps': 0.6,
            'total_fees': -3.25,  # Negative (rebate)
            'fee_rate_bps': -2.5,
            'fill_rate': 100.0,
            'total_cost': 64878.75,
            'chunk_count': 3,
            'price_range': {'min': 43250.0, 'max': 43255.0}
        }
        
        with patch.object(Trade_BTC, 'send_tg') as mock_send_tg:
            Trade_BTC.send_order_completion_telegram(
                "BTCUSDT", "buy", 1.5, test_metrics, 45.2
            )
            
            # Verify send_tg was called
            mock_send_tg.assert_called_once()
            
            # Check that the message contains maker-only information
            message = mock_send_tg.call_args[0][0]
            self.assertIn("MAKER-ONLY EXECUTION", message)
            self.assertIn("postOnly", message)
            self.assertIn("maker rebates", message)
            print("✅ Telegram notification includes maker-only information")

    def test_configuration_validation(self):
        """Test that configuration is set correctly for maker-only mode"""
        print("\n🧪 TEST 12: Configuration Validation")
        
        # Verify maker-only mode is enabled
        self.assertTrue(Trade_BTC.MAKER_ONLY_MODE)
        print("✅ MAKER_ONLY_MODE = True")
        
        # Verify infinite retry mode is enabled  
        self.assertTrue(Trade_BTC.INFINITE_RETRY_MODE)
        print("✅ INFINITE_RETRY_MODE = True")
        
        # Verify increased timeouts for maker orders
        self.assertGreaterEqual(Trade_BTC.SMALL_ORDER_MAX_ATTEMPTS, 15)
        self.assertGreaterEqual(Trade_BTC.LARGE_ORDER_MAX_ATTEMPTS, 10)
        print("✅ Increased attempt limits for maker-only execution")
        
        # Verify extended timeouts for multiprocessing
        self.assertGreaterEqual(Trade_BTC.CHUNK_MULTIPROCESSING_TIMEOUT, 300)
        self.assertGreaterEqual(Trade_BTC.CHUNK_PROCESS_TIMEOUT, 180)
        print("✅ Extended timeouts for patient maker execution")

def run_comprehensive_test():
    """Run all test scenarios"""
    print("🧪" + "="*80)
    print("🧪 COMPREHENSIVE MAKER-ONLY TRADE_BTC.PY TEST SUITE")
    print("🧪" + "="*80)
    print("🎯 Testing maker-only execution, infinite retry, and multiprocessing")
    print("💰 Validating cost optimization and error handling")
    print("🚫 Confirming no market order fallbacks exist")
    print("="*82)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestMakerOnlyTrading)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)
    
    print("\n" + "="*82)
    print("🧪 TEST SUMMARY")
    print("="*82)
    
    if result.wasSuccessful():
        print("✅ ALL TESTS PASSED!")
        print("🎯 Maker-only execution system is working correctly")
        print("💰 Cost optimization features validated")
        print("🔄 Infinite retry logic tested")
        print("🚀 Multiprocessing chunked orders verified")
        print("📊 Analytics and notifications confirmed")
        print("\n🟢 SYSTEM READY FOR LIVE TRADING")
    else:
        print("❌ SOME TESTS FAILED!")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
        print("\n🔴 REVIEW ISSUES BEFORE LIVE TRADING")
    
    print("="*82)

if __name__ == "__main__":
    run_comprehensive_test() 