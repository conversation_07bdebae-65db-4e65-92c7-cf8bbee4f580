import pandas as pd
import numpy as np
import datetime
import time
import requests
import ccxt
import config
from concurrent.futures import ThreadPoolExecutor

# 設置 pandas 顯示選項
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)

# 交易所設置，啟用速率限制
EXCHANGE = ccxt.bybit({
    'apiKey': '',
    'secret': '',
    'enableRateLimit': True,  # 啟用 CCXT 內建速率限制
    'rateLimit': 100,  # 設置每秒最大請求間隔（毫秒），相當於每秒約 15 次請求
})

# 交易參數
TIMEOUT = 300  # 5分鐘全局超時

# 🚨 NEW: 添加全局时间跟踪
PROGRAM_START_TIME = None

def check_timeout():
    """🚨 NEW: 检查是否超过5分钟"""
    global PROGRAM_START_TIME
    if PROGRAM_START_TIME is None:
        return False
    return time.time() - PROGRAM_START_TIME > TIMEOUT

def cleanup_all_orders():
    """🚨 NEW: 检查并清理所有未完成订单"""
    print("🔍 检查所有未完成的限价订单...")
    
    all_orders = []  # 存储所有找到的订单
    
    try:
        assets = load_config()
        
        # 第1步：检查所有订单
        for asset_config in assets:
            # 🚨 ADDED: 检查SYMBOL字段是否存在
            if 'SYMBOL' not in asset_config:
                print(f"⚠️ {asset_config.get('ASSET', 'Unknown')} 配置缺少SYMBOL字段，跳过")
                continue
                
            symbol = asset_config['SYMBOL']
            try:
                open_orders = EXCHANGE.fetch_open_orders(symbol, params={'category': 'linear'})
                if open_orders:
                    print(f"📋 {symbol} 发现 {len(open_orders)} 个未完成订单:")
                    for order in open_orders:
                        order_info = {
                            'symbol': symbol,
                            'id': order['id'],
                            'side': order['side'],
                            'amount': order['amount'],
                            'price': order['price'],
                            'status': order['status']
                        }
                        all_orders.append(order_info)
                        print(f"   - 订单ID: {order['id']}, {order['side']} {order['amount']} @ {order['price']}")
                else:
                    print(f"✅ {symbol} 没有未完成订单")
            except Exception as e:
                print(f"❌ 检查 {symbol} 订单失败: {e}")
        
        # 第2步：显示总结
        if all_orders:
            print(f"\n📊 总共发现 {len(all_orders)} 个未完成的限价订单")
            print("🧹 开始取消所有订单...")
            
            # 第3步：取消所有订单
            cancelled_count = 0
            for order_info in all_orders:
                try:
                    EXCHANGE.cancel_order(order_info['id'], order_info['symbol'], params={'category': 'linear'})
                    print(f"✅ 已取消 {order_info['symbol']} 订单: {order_info['id']}")
                    cancelled_count += 1
                except Exception as e:
                    print(f"❌ 取消 {order_info['symbol']} 订单 {order_info['id']} 失败: {e}")
            
            print(f"\n✅ 成功取消 {cancelled_count}/{len(all_orders)} 个订单")
        else:
            print("✅ 没有发现任何未完成的限价订单")
            
    except Exception as e:
        print(f"❌ 检查订单过程失败: {e}")
    
    print("🧹 订单清理完成")

def load_config():
    """從 config.py 讀取配置"""
    try:
        return config.STRATEGIES
    except AttributeError as e:
        print(f"讀取 config.py 失敗: {e}")
        return []

def current_pos(symbol):
    """取得目前持倉"""
    try:
        position = EXCHANGE.fetch_position(symbol, params={'category': 'linear'})['info']
        if position['side'] == 'Buy':
            return float(position['size'])
        elif position['side'] == 'Sell':
            return -float(position['size'])
        return 0
    except Exception as e:
        print(f"獲取 {symbol} 持倉失敗: {e}")
        return 0

def execute_trade(signal, max_pos, symbol, min_order_size):
    """
    根據訊號執行限價單交易，持續嘗試直到達到目標部位
    """
    net_pos = current_pos(symbol)
    target_pos = max_pos * signal
    initial_bet_size = round(target_pos - net_pos, 3)

    if abs(initial_bet_size) < min_order_size:
        print(f"{symbol} bet_size 太小，忽略: {initial_bet_size}")
        return

    order_check_interval = 3

    # 🚨 CHANGED: while True -> while not check_timeout()
    while not check_timeout():
        try:
            current_net_pos = current_pos(symbol)
            remaining_size = round(target_pos - current_net_pos, 3)
            
            if abs(remaining_size) < min_order_size:
                print(f"{symbol} 目標部位已達成")
                return

            order_book = EXCHANGE.fetch_order_book(symbol, params={'category': 'linear'})
            bids = order_book['bids']
            asks = order_book['asks']

            # 使用 bid1 和 ask1
            bid1 = bids[0][0]  # 第一檔買價
            ask1 = asks[0][0]  # 第一檔賣價

            limit_price = bid1 if remaining_size > 0 else ask1
            side = 'buy' if remaining_size > 0 else 'sell'
            amount = abs(remaining_size)
            amount = min(max_pos, amount)

            open_orders = EXCHANGE.fetch_open_orders(symbol, params={'category': 'linear'})
            for open_order in open_orders:
                EXCHANGE.cancel_order(open_order['id'], symbol, params={'category': 'linear'})
                print(f"取消 {symbol} 未成交訂單: {open_order['id']}")

            print(f"嘗試下單: {side} {amount} {symbol} @ {limit_price}")
            order = EXCHANGE.create_order(symbol, 'limit', side, amount, limit_price, params={'category': 'linear'})
            order_id = order['id']
            order_start_time = time.time()

            # 🚨 CHANGED: while True -> while not check_timeout()
            while not check_timeout():
                time.sleep(order_check_interval)
                
                try:
                    # 使用 fetch_open_orders 檢查訂單是否尚未完全成交
                    open_orders = EXCHANGE.fetch_open_orders(symbol, params={'category': 'linear'})
                    order_status = None
                    for open_order in open_orders:
                        if open_order['id'] == order_id:
                            order_status = open_order
                            break

                    if order_status and time.time() - order_start_time <= order_check_interval:
                        # 訂單尚未完全成交（未結或部分成交）
                        filled_amount = float(order_status.get('filled', 0))
                        remaining_amount = amount - filled_amount
                        print(f"{symbol} 訂單尚未完全成交: 已成交={filled_amount}, 未成交={remaining_amount}")
                        continue  # 繼續等待，直到超時
                    elif order_status and time.time() - order_start_time > order_check_interval:
                        # 超時，取消尚未完全成交的訂單
                        filled_amount = float(order_status.get('filled', 0))
                        remaining_amount = amount - filled_amount
                        print(f"{symbol} 訂單超時，尚未完全成交: 已成交={filled_amount}, 未成交={remaining_amount}")
                        EXCHANGE.cancel_order(order_id, symbol, params={'category': 'linear'})
                        print(f"{symbol} 取消尚未完全成交的訂單: {order_id}")
                        break
                    else:
                        # 訂單不在未結訂單中，假設已完全成交或取消
                        print(f"{symbol} 訂單不在未結訂單中，假設已完全成交或取消")
                        break

                except Exception as e:
                    print(f"檢查 {symbol} 訂單狀態時出錯: {e}")
                    break

            # 🚨 ADDED: 如果超时，取消当前订单并退出
            if check_timeout():
                try:
                    EXCHANGE.cancel_order(order_id, symbol, params={'category': 'linear'})
                    print(f"⏰ {symbol} 超时，取消订单: {order_id}")
                except:
                    pass
                return

            # 檢查最新代幣持倉並計算最新 bet_size
            current_net_pos = current_pos(symbol)
            latest_bet_size = round(target_pos - current_net_pos, 3)
            print(f"{symbol} 最新代幣持倉: {current_net_pos}, 最新 bet_size: {latest_bet_size}, 目標持倉: {target_pos}, 剩餘需要交易: {latest_bet_size}")
            time.sleep(1)

        except Exception as e:
            print(f"{symbol} 交易執行出錯: {e}")
            time.sleep(1)
            continue

    # 🚨 ADDED: 超时退出消息
    print(f"⏰ {symbol} 达到5分钟超时，退出交易")

def trade_asset(asset_config, max_execution_time):
    start_time = time.time()
    asset = asset_config['ASSET']
    
    # 🚨 ADDED: 检查SYMBOL字段是否存在
    if 'SYMBOL' not in asset_config:
        print(f"⚠️ {asset} 配置缺少SYMBOL字段，跳过交易")
        return
    
    symbol = asset_config['SYMBOL']
    max_pos = asset_config['MAX_POS']
    min_order_size = asset_config['min_order_size']

    try:
        signal_file = f'sub_account3/signal/{asset}_signal.csv'
        df = pd.read_csv(signal_file)
        signal = df['overall_pos'].iloc[-1]
        print(f"{symbol} signal_from_csv: {signal}")

        # 提前檢查是否需要交易
        net_pos = current_pos(symbol)
        target_pos = max_pos * signal
        if abs(target_pos - net_pos) < min_order_size:
            print(f"{symbol} 信號無需交易，跳過")
            return

        order_book = EXCHANGE.fetch_order_book(symbol, params={'category': 'linear'})
        bid1 = order_book['bids'][0][0]
        ask1 = order_book['asks'][0][0]
        
        if bid1 and ask1:
            price = (bid1 + ask1) / 2
            
            # 🚨 CHANGED: while True -> while not check_timeout()
            while not check_timeout():
                if time.time() - start_time > max_execution_time:
                    print(f"{symbol} 程式運行超過 {max_execution_time/60} 分鐘，退出執行")
                    open_orders = EXCHANGE.fetch_open_orders(symbol, params={'category': 'linear'})
                    for order in open_orders:
                        try:
                            EXCHANGE.cancel_order(order['id'], symbol, params={'category': 'linear'})
                            print(f"取消 {symbol} 未完成訂單: {order['id']}")
                        except Exception as e:
                            print(f"取消 {symbol} 訂單失敗: {e}")
                    break
                
                execute_trade(signal, max_pos, symbol, min_order_size)
                
                current_pos_value = current_pos(symbol)
                if abs(max_pos * signal - current_pos_value) < min_order_size:
                    print(f"{symbol} 已達到目標倉位")
                    break
            
    except Exception as e:
        print(f"{symbol} 程式執行錯誤: {e}")

def main():
    # 🚨 ADDED: 设置全局开始时间
    global PROGRAM_START_TIME
    PROGRAM_START_TIME = time.time()
    
    start_time = time.time()
    max_execution_time = TIMEOUT
    assets = load_config()
    if not assets:
        print("無資產配置，程式退出")
        return

    print(f"🚀 程序开始，将在5分钟后自动退出")  # 🚨 ADDED: 提示信息

    batch_size = 6
    for i in range(0, len(assets), batch_size):
        # 🚨 ADDED: 每批次前检查超时
        if check_timeout():
            print("⏰ 达到5分钟限制，退出程序")
            break
            
        batch = assets[i:i + batch_size]
        with ThreadPoolExecutor(max_workers=batch_size) as executor:
            executor.map(lambda x: trade_asset(x, max_execution_time), batch)
        time.sleep(1)

    # 🚨 ADDED: 程序结束前清理所有订单
    cleanup_all_orders()
    print("✅ 程序已安全退出")  # 🚨 ADDED: 退出信息

if __name__ == "__main__":
    time.sleep(10)
    start_time = time.time()
    print(f"開始時間: {time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(start_time))}")
    
    # 🚨 ADDED: try-except-finally 块
    try:
        main()
    except KeyboardInterrupt:
        print("⚠️ 程序被手动中断")
        cleanup_all_orders()
    except Exception as e:
        print(f"💥 程序异常: {e}")
        cleanup_all_orders()
    finally:
        end_time = time.time()
        print(f"結束時間: {time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(end_time))}")
        print(f"執行時間: {end_time - start_time:.2f} 秒")