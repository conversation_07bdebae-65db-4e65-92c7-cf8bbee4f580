import ccxt
import pandas as pd
import numpy as np
import datetime
import time
import requests
import multiprocessing as mp  # Added for multiprocessing support
from multiprocessing import Pool, Manager, Queue, Process, Value, Lock
from concurrent.futures import ProcessPoolExecutor, as_completed, TimeoutError
import yaml  # install package PyYAML
import warnings
import os
from pprint import pprint
from Models import Strategy  # Import the Strategy class from the new file
import math  # Added to calculate precision
import gc
import random  # For jitter in exponential backoff
import shutil  # Added for file migration

# Import BybitAPI from bybit.py
from bybit import BybitAPI  # Import the Bybit API class for exchange data

# ===== USER CONFIGURABLE SETTINGS =====
# Strategy processing delay settings
STRATEGY_MIN_DELAY = 1  # Minimum random delay between strategy processing (seconds)
STRATEGY_MAX_DELAY = 10  # Maximum random delay between strategy processing (seconds)

# Order execution settings
ORDER_TIMEOUT_SECONDS = 10  # Timeout for each order attempt (reduced from 30 for faster cancellation)
ORDER_SPREAD_PERCENTAGE = 0.00005  # Price improvement percentage (0.005% for tighter spreads)
ORDER_CHECK_INTERVAL_FAST = 2  # Fast checking interval (reduced from 5 seconds)

# Order size classification settings
LARGE_ORDER_THRESHOLD = 1.0  # Orders above this size are considered "large" (in asset units)
LARGE_ORDER_USD_THRESHOLD = 15000  # Increase from $10k to $15k

# Small order settings (aggressive execution) - MAKER ONLY
SMALL_ORDER_TIMEOUT = 15  # Slightly longer timeout for maker orders
SMALL_ORDER_SPREAD = 0.00005  # Tight spread (0.005%)
SMALL_ORDER_CHECK_INTERVAL = 2  # Fast checking
SMALL_ORDER_MAX_ATTEMPTS = 20  # More attempts since no market order fallback

# Large order settings (OPTIMIZED for fast chunked execution) - MAKER ONLY
LARGE_ORDER_TIMEOUT = 30  # Reduced from 90s to 30s for faster chunk execution
LARGE_ORDER_SPREAD = 0.00005  # Tighter spread (0.005%) same as small orders for better fills
LARGE_ORDER_CHECK_INTERVAL = 2  # Faster checking (reduced from 5s to 2s)
LARGE_ORDER_MAX_ATTEMPTS = 10  # Reduced attempts for faster timeout

# Dynamic chunk sizing based on USDT value (replaces fixed LARGE_ORDER_CHUNK_SIZE)
CHUNK_TARGET_USDT_VALUE = 2000.0  # Target USDT value per chunk (increased for efficiency)
MIN_CHUNK_USDT_VALUE = 100.0  # Minimum USDT value per chunk (prevents tiny chunks)
MAX_CHUNK_USDT_VALUE = 10000.0  # Maximum USDT value per chunk (increased for large orders)
MAX_CHUNKS_PER_ORDER = 50  # Maximum number of chunks to prevent infinite loops (reduced for efficiency)

# MAKER-ONLY MODE: No market order fallbacks, all orders must be maker orders
MAKER_ONLY_MODE = True  # Force all orders to be maker orders (postOnly = True)
INFINITE_RETRY_MODE = True  # Keep retrying until target size is filled

# ⚠️ IMPORTANT: This configuration removes ALL market order fallbacks
# - All orders use postOnly = True to guarantee maker execution
# - System will retry indefinitely until complete fills
# - No taker fees, only maker rebates/lower fees
# - May take longer to fill in thin order books
# - Prioritizes cost savings over execution speed

# Multiprocessing settings for chunked orders - OPTIMIZED FOR SPEED
CHUNK_MULTIPROCESSING_TIMEOUT = 180  # Total timeout for all chunks (3 minutes - much faster)
MAX_CHUNK_WORKERS = 8  # Increased parallel chunk workers for faster execution
CHUNK_PROCESS_TIMEOUT = 60  # Timeout per individual chunk process (1 minute - much faster)

# Glassnode API retry settings
GLASSNODE_MAX_RETRIES = 5  # Maximum number of retry attempts for Glassnode API
GLASSNODE_RETRY_DELAY = 10  # Base delay between Glassnode API retries (seconds)

# Bybit VIP level rate limits (requests per second)
BYBIT_RATE_LIMITS = {
    '0': 10,  # Default
    '1': 20,  # VIP 1
    '2': 40,  # VIP 2
    '3': 60,  # VIP 3
    '4': 60,  # VIP 4
    '5': 60,  # VIP 5
    'S': 60,  # VIP S
    'Default': 10  # Fallback
}

# OKX rate limit handling
OKX_MAX_RETRIES = 5
OKX_BASE_DELAY = 2  # Base delay in seconds

# ===== STRATEGY PERFORMANCE BACKUP AND RECOVERY SETTINGS =====
BACKUP_RETENTION_HOURS = 24  # Keep backups for 24 hours
MAX_BACKUP_FILES = 10  # Maximum number of backup files to keep


def create_strategy_performance_backup(performance_file):
    """
    Create a timestamped backup of the strategy_performance.csv file.

    Args:
        performance_file (str): Path to the strategy_performance.csv file

    Returns:
        str: Path to the created backup file, or None if backup failed
    """
    try:
        if not os.path.exists(performance_file):
            print(f"Warning: Cannot backup {performance_file} - file does not exist")
            return None

        # Create backup directory if it doesn't exist
        backup_dir = os.path.join(os.path.dirname(performance_file), 'backups')
        os.makedirs(backup_dir, exist_ok=True)

        # Create timestamped backup filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"strategy_performance_backup_{timestamp}.csv"
        backup_path = os.path.join(backup_dir, backup_filename)

        # Copy the file
        shutil.copy2(performance_file, backup_path)
        print(f"Created backup: {backup_path}")

        # Clean up old backups
        cleanup_old_backups(backup_dir)

        return backup_path

    except Exception as e:
        print(f"Error creating backup of strategy_performance.csv: {e}")
        return None


def cleanup_old_backups(backup_dir):
    """
    Clean up old backup files based on retention policy.

    Args:
        backup_dir (str): Directory containing backup files
    """
    try:
        if not os.path.exists(backup_dir):
            return

        # Get all backup files
        backup_files = []
        for filename in os.listdir(backup_dir):
            if filename.startswith('strategy_performance_backup_') and filename.endswith('.csv'):
                filepath = os.path.join(backup_dir, filename)
                mtime = os.path.getmtime(filepath)
                backup_files.append((filepath, mtime))

        # Sort by modification time (newest first)
        backup_files.sort(key=lambda x: x[1], reverse=True)

        # Remove files older than retention period
        cutoff_time = time.time() - (BACKUP_RETENTION_HOURS * 3600)
        files_to_remove = []

        for filepath, mtime in backup_files:
            if mtime < cutoff_time:
                files_to_remove.append(filepath)

        # Also remove excess files beyond MAX_BACKUP_FILES
        if len(backup_files) > MAX_BACKUP_FILES:
            files_to_remove.extend([f[0] for f in backup_files[MAX_BACKUP_FILES:]])

        # Remove old backup files
        for filepath in files_to_remove:
            try:
                os.remove(filepath)
                print(f"Removed old backup: {os.path.basename(filepath)}")
            except Exception as e:
                print(f"Error removing old backup {filepath}: {e}")

    except Exception as e:
        print(f"Error during backup cleanup: {e}")


def validate_weight_ratios(performance_df):
    """
    Validate that weight_ratio values are not corrupted (empty or invalid).

    Args:
        performance_df (pd.DataFrame): DataFrame containing strategy performance data

    Returns:
        tuple: (is_valid, corrupted_strategies, total_strategies)
    """
    try:
        if 'weight_ratio' not in performance_df.columns:
            return False, [], len(performance_df)

        corrupted_strategies = []
        total_strategies = len(performance_df)

        for idx, row in performance_df.iterrows():
            strategy_name = row.get('strategy_name', f'Unknown_{idx}')
            weight_ratio = row.get('weight_ratio', '')

            # Check if weight_ratio is empty, NaN, or invalid
            if pd.isna(weight_ratio) or weight_ratio == '' or weight_ratio is None:
                corrupted_strategies.append(strategy_name)
            else:
                # Try to convert to float to check if it's a valid number
                try:
                    float(weight_ratio)
                except (ValueError, TypeError):
                    corrupted_strategies.append(strategy_name)

        is_valid = len(corrupted_strategies) == 0
        return is_valid, corrupted_strategies, total_strategies

    except Exception as e:
        print(f"Error validating weight ratios: {e}")
        return False, [], 0


def find_latest_valid_backup(backup_dir):
    """
    Find the most recent backup file with valid weight_ratio values.

    Args:
        backup_dir (str): Directory containing backup files

    Returns:
        str: Path to the latest valid backup file, or None if none found
    """
    try:
        if not os.path.exists(backup_dir):
            return None

        # Get all backup files sorted by modification time (newest first)
        backup_files = []
        for filename in os.listdir(backup_dir):
            if filename.startswith('strategy_performance_backup_') and filename.endswith('.csv'):
                filepath = os.path.join(backup_dir, filename)
                mtime = os.path.getmtime(filepath)
                backup_files.append((filepath, mtime))

        backup_files.sort(key=lambda x: x[1], reverse=True)

        # Check each backup file for valid weight_ratios
        for filepath, mtime in backup_files:
            try:
                backup_df = pd.read_csv(filepath)
                is_valid, corrupted_strategies, total_strategies = validate_weight_ratios(backup_df)

                if is_valid and total_strategies > 0:
                    print(f"Found valid backup: {os.path.basename(filepath)} with {total_strategies} strategies")
                    return filepath
                else:
                    print(
                        f"Backup {os.path.basename(filepath)} has corrupted weight_ratios: {len(corrupted_strategies)}/{total_strategies} corrupted")

            except Exception as e:
                print(f"Error reading backup {filepath}: {e}")
                continue

        print("No valid backups found with intact weight_ratio values")
        return None

    except Exception as e:
        print(f"Error searching for valid backup: {e}")
        return None


def restore_from_backup(performance_file, backup_file):
    """
    Restore strategy_performance.csv from a backup file.

    Args:
        performance_file (str): Path to the main strategy_performance.csv file
        backup_file (str): Path to the backup file to restore from

    Returns:
        bool: True if restoration was successful, False otherwise
    """
    try:
        if not os.path.exists(backup_file):
            print(f"Error: Backup file {backup_file} does not exist")
            return False

        # Read and validate the backup file
        backup_df = pd.read_csv(backup_file)
        is_valid, corrupted_strategies, total_strategies = validate_weight_ratios(backup_df)

        if not is_valid:
            print(f"Error: Backup file {backup_file} also has corrupted weight_ratios")
            return False

        # Create a backup of the current corrupted file before restoring
        corrupted_backup_dir = os.path.join(os.path.dirname(performance_file), 'corrupted_backups')
        os.makedirs(corrupted_backup_dir, exist_ok=True)

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        corrupted_backup_path = os.path.join(corrupted_backup_dir, f"corrupted_strategy_performance_{timestamp}.csv")

        if os.path.exists(performance_file):
            shutil.copy2(performance_file, corrupted_backup_path)
            print(f"Saved corrupted file as: {corrupted_backup_path}")

        # Restore from backup
        shutil.copy2(backup_file, performance_file)
        print(f"Successfully restored strategy_performance.csv from backup: {os.path.basename(backup_file)}")
        print(f"Restored {total_strategies} strategies with valid weight_ratios")

        return True

    except Exception as e:
        print(f"Error restoring from backup: {e}")
        return False


def safe_update_strategy_performance(performance_file, strategy_name, updates):
    """
    Safely update strategy performance metrics while protecting weight_ratio values.

    Args:
        performance_file (str): Path to the strategy_performance.csv file
        strategy_name (str): Name of the strategy to update
        updates (dict): Dictionary of column names and values to update

    Returns:
        bool: True if update was successful, False otherwise
    """
    try:
        # Create backup before any modifications
        backup_path = create_strategy_performance_backup(performance_file)

        if not os.path.exists(performance_file):
            print(f"Error: Performance file {performance_file} does not exist")
            return False

        # Read current data
        existing_df = pd.read_csv(performance_file)

        # Validate current weight_ratios before modification
        is_valid_before, corrupted_before, total_before = validate_weight_ratios(existing_df)

        if not is_valid_before:
            print(f"Warning: Found {len(corrupted_before)} corrupted weight_ratios before update: {corrupted_before}")

            # Try to restore from backup
            backup_dir = os.path.join(os.path.dirname(performance_file), 'backups')
            valid_backup = find_latest_valid_backup(backup_dir)

            if valid_backup:
                print("Attempting to restore from valid backup before proceeding with update...")
                if restore_from_backup(performance_file, valid_backup):
                    # Re-read the restored data
                    existing_df = pd.read_csv(performance_file)
                else:
                    print("Failed to restore from backup. Proceeding with caution...")
            else:
                print("No valid backup found. Proceeding with caution...")

        # Check if strategy exists
        if strategy_name not in existing_df['strategy_name'].values:
            print(f"Warning: Strategy '{strategy_name}' not found in performance file")
            return False

        # Find the strategy index
        strategy_idx = existing_df.index[existing_df['strategy_name'] == strategy_name].tolist()[0]

        # Preserve the existing weight_ratio value
        existing_weight_ratio = existing_df.at[
            strategy_idx, 'weight_ratio'] if 'weight_ratio' in existing_df.columns else ''

        # Apply updates (excluding weight_ratio)
        for column, value in updates.items():
            if column != 'weight_ratio':  # Never allow weight_ratio to be updated through this function
                existing_df.at[strategy_idx, column] = value

        # Ensure weight_ratio is preserved
        if 'weight_ratio' in existing_df.columns:
            existing_df.at[strategy_idx, 'weight_ratio'] = existing_weight_ratio

        # Validate weight_ratios after modification
        is_valid_after, corrupted_after, total_after = validate_weight_ratios(existing_df)

        if not is_valid_after:
            print(f"Error: Weight ratios became corrupted during update. Corrupted strategies: {corrupted_after}")

            # Restore from backup if available
            if backup_path and os.path.exists(backup_path):
                print("Restoring from backup due to corruption...")
                return restore_from_backup(performance_file, backup_path)
            else:
                print("No backup available for restoration")
                return False

        # Save the updated DataFrame
        existing_df.to_csv(performance_file, index=False)
        print(f"Successfully updated strategy '{strategy_name}' performance metrics")

        return True

    except Exception as e:
        print(f"Error in safe_update_strategy_performance: {e}")

        # Try to restore from backup if available
        if 'backup_path' in locals() and backup_path and os.path.exists(backup_path):
            print("Attempting to restore from backup due to error...")
            restore_from_backup(performance_file, backup_path)

        return False


def check_and_repair_weight_ratios(performance_file):
    """
    Check for corrupted weight_ratios and attempt automatic repair.

    Args:
        performance_file (str): Path to the strategy_performance.csv file

    Returns:
        bool: True if file is valid or was successfully repaired, False otherwise
    """
    try:
        if not os.path.exists(performance_file):
            print(f"Performance file {performance_file} does not exist")
            return False

        # Read and validate current file
        current_df = pd.read_csv(performance_file)
        is_valid, corrupted_strategies, total_strategies = validate_weight_ratios(current_df)

        if is_valid:
            print(f"Weight ratios are valid for all {total_strategies} strategies")
            return True

        print(f"Found {len(corrupted_strategies)} corrupted weight_ratios out of {total_strategies} strategies")
        print(f"Corrupted strategies: {corrupted_strategies}")

        # Try to find and restore from a valid backup
        backup_dir = os.path.join(os.path.dirname(performance_file), 'backups')
        valid_backup = find_latest_valid_backup(backup_dir)

        if valid_backup:
            print(f"Found valid backup, attempting restoration...")
            if restore_from_backup(performance_file, valid_backup):
                print("Successfully repaired weight_ratios from backup")
                return True
            else:
                print("Failed to restore from backup")
        else:
            print("No valid backup found for automatic repair")

        # Send alert about corruption
        try:
            send_tg(
                f"⚠️ CRITICAL: {SYMBOL} strategy_performance.csv weight_ratios corrupted! {len(corrupted_strategies)}/{total_strategies} strategies affected. Manual intervention may be required.")
        except:
            print("Could not send corruption alert via Telegram")

        return False

    except Exception as e:
        print(f"Error checking and repairing weight ratios: {e}")
        return False


def categorize_trading_error(error_msg, attempt=0, max_attempts=3):
    """
    Categorize trading errors to determine if they are fatal or retryable.

    Args:
        error_msg (str): The error message to categorize
        attempt (int): Current attempt number (0-based)
        max_attempts (int): Maximum number of attempts

    Returns:
        tuple: (is_fatal, error_category, user_friendly_message)
    """
    error_lower = error_msg.lower()

    # Check for specific Binance error codes first
    if 'binance' in error_lower and '-4164' in error_msg:
        return True, "Minimum Notional Value", "Order size is below exchange minimum notional value requirement"

    # Define error categories with keywords and whether they're fatal
    error_categories = {
        "Insufficient Balance": {
            "keywords": ['insufficient', 'not enough', 'balance', 'margin', 'fund'],
            "fatal": True,
            "message": "Account balance is insufficient for this trade"
        },
        "Minimum Notional Value": {
            "keywords": ['notional must be no smaller', 'notional', 'minimum notional', 'min notional'],
            "fatal": True,
            "message": "Order size is below exchange minimum notional value requirement"
        },
        "Invalid Parameters": {
            "keywords": ['invalid', 'parameter', 'size', 'precision', 'minimum'],
            "fatal": True,
            "message": "Order parameters are invalid"
        },
        "API Key Error": {
            "keywords": ['api key'],
            "fatal": True,
            "message": "API key is invalid or missing"
        },
        "Symbol/Trading Disabled": {
            "keywords": ['symbol', 'disabled', 'not found'],
            "fatal": True,
            "message": "Symbol is invalid or trading is disabled"
        },
        "Authentication Error": {
            "keywords": ['unauthorized', 'authentication', 'signature', 'permission'],
            "fatal": True,
            "message": "API authentication failed"
        },
        "Market/Trading Unavailable": {
            "keywords": ['market closed', 'trading suspended', 'maintenance', 'market', 'closed'],
            "fatal": True,
            "message": "Trading is currently unavailable"
        },
        "Position/Risk Limit": {
            "keywords": ['position limit', 'max position', 'risk limit', 'leverage'],
            "fatal": True,
            "message": "Position or risk limits exceeded"
        },
        "Network/Connection Issue": {
            "keywords": ['network', 'timeout', 'connection', 'rate limit'],
            "fatal": False,
            "message": "Network or connection issue (retryable)"
        },
        "Server Error": {
            "keywords": ['server error', 'internal error', '500', '502', '503'],
            "fatal": False,
            "message": "Server error (retryable)"
        }
    }

    # Check each category
    for category, config in error_categories.items():
        if any(keyword in error_lower for keyword in config["keywords"]):
            is_fatal = config["fatal"]
            return is_fatal, category, config["message"]

    # For unknown errors, treat as fatal only on the last attempt
    is_fatal = (attempt >= max_attempts - 1)
    return is_fatal, "Unknown Error", "Unknown error occurred"


def exponential_backoff(attempt, base_delay=OKX_BASE_DELAY, max_delay=60):
    """
    Calculate delay time using exponential backoff with jitter.

    Args:
        attempt: Current retry attempt (0-based)
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds

    Returns:
        float: Time to wait in seconds
    """
    # Calculate exponential backoff: base_delay * 2^attempt
    delay = min(base_delay * (2 ** attempt), max_delay)

    # Add jitter (±20%) to avoid thundering herd problem
    jitter = random.uniform(-0.2, 0.2) * delay

    return delay + jitter


def retry_api_call(func, *args, max_retries=OKX_MAX_RETRIES, **kwargs):
    """
    Execute an API call with retry logic and exponential backoff.

    Args:
        func: Function to call
        *args: Arguments to pass to func
        max_retries: Maximum number of retry attempts
        **kwargs: Keyword arguments to pass to func

    Returns:
        The result of the function call

    Raises:
        Exception: If all retry attempts fail
    """
    last_exception = None

    for attempt in range(max_retries):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            last_exception = e
            error_msg = str(e)

            # Check if this is a rate limit error
            if "Too Many Requests" in error_msg or "rate limit" in error_msg.lower():
                # Calculate delay using exponential backoff
                delay = exponential_backoff(attempt)
                print(f"Rate limit hit. Retry {attempt + 1}/{max_retries} after {delay:.2f}s delay")
                time.sleep(delay)
            else:
                # For other errors, use a smaller delay
                print(f"API error: {error_msg}. Retry {attempt + 1}/{max_retries} after 1s delay")
                time.sleep(1)

    # If we've exhausted all retries, raise the last exception
    print(f"Failed after {max_retries} retry attempts")
    raise last_exception


def retry_glassnode_request(url, params, max_retries=GLASSNODE_MAX_RETRIES, retry_delay=GLASSNODE_RETRY_DELAY):
    """
    Execute a Glassnode API request with specific retry logic for Glassnode API.

    Args:
        url: The Glassnode API URL
        params: Request parameters
        max_retries: Maximum number of retry attempts
        retry_delay: Base delay between retries in seconds

    Returns:
        The API response if successful

    Raises:
        Exception: If all retry attempts fail
    """
    last_exception = None

    for attempt in range(max_retries):
        try:
            response = requests.get(url, params=params)

            # Check for HTTP errors
            if response.status_code != 200:
                error_msg = f"Glassnode API error: Status code {response.status_code}"
                print(error_msg)

                # If we hit rate limit, use longer delay
                if response.status_code == 429:
                    actual_delay = retry_delay + random.randint(0, 5)  # Add some randomness
                    print(f"Rate limit hit. Retry {attempt + 1}/{max_retries} after {actual_delay}s delay")
                else:
                    actual_delay = retry_delay
                    print(f"API error. Retry {attempt + 1}/{max_retries} after {actual_delay}s delay")

                # Last attempt - raise exception
                if attempt == max_retries - 1:
                    raise Exception(error_msg)

                # Wait before retry
                time.sleep(actual_delay)
                continue

            # Success - return the response
            return response

        except Exception as e:
            last_exception = e
            print(f"Glassnode request error: {str(e)}")

            # Last attempt - raise the exception
            if attempt == max_retries - 1:
                print(f"Failed after {max_retries} retry attempts")
                raise

            # Wait before retry
            actual_delay = retry_delay + random.randint(0, 5)  # Add some randomness
            print(f"Retrying in {actual_delay} seconds...")
            time.sleep(actual_delay)

    # Fallback - should never reach here but just in case
    raise last_exception if last_exception else Exception("Unknown error in Glassnode API request")


def ensure_exchange_credentials():
    """
    Ensure that the global EXCHANGE object has all required credentials.
    Reinitialize the exchange if necessary.

    Returns:
        bool: True if credentials are valid, False otherwise
    """
    global EXCHANGE, keys, selected_exchange

    if selected_exchange != "OKX":
        # Only OKX needs special passphrase handling
        return True

    try:
        # Test an authenticated API call with retry logic
        max_retries = 5  # Increased from 3 to 5

        for attempt in range(max_retries):
            try:
                # Use a simple authenticated call to test credentials
                EXCHANGE.fetch_balance()
                return True
            except Exception as e:
                error_msg = str(e)

                # Check if this is a rate limit error
                if "Too Many Requests" in error_msg or "rate limit" in error_msg.lower() or "10006" in error_msg:
                    if attempt < max_retries - 1:
                        # Calculate delay using exponential backoff
                        delay = exponential_backoff(attempt)
                        print(
                            f"Rate limit hit while verifying credentials. Retry {attempt + 1}/{max_retries} after {delay:.2f}s delay")
                        time.sleep(delay)
                        continue
                    else:
                        print(f"Failed to verify credentials after {max_retries} attempts due to rate limits")
                        # Return true anyway but warn about possible issues
                        print("WARNING: Could not properly verify credentials due to rate limits")
                        return True  # Return true to avoid further retries

                # Check for authentication errors
                if 'passphrase' in str(e).lower() or 'password' in str(e).lower() or 'api key' in str(e).lower():
                    # If it's the last retry, reinitialize the exchange
                    if attempt == max_retries - 1:
                        # Reinitialize the exchange with proper credentials
                        api_key = keys.get('APIKEY', '')
                        secret = keys.get('SECRET', '')
                        passphrase = keys.get('PASSPHRASE', '')
                        live_account = keys.get('LIVE_ACCOUNT', 'N')

                        print("Reinitializing OKX exchange with proper credentials")
                        if not passphrase:
                            print("WARNING: OKX requires a passphrase for authenticated API calls")
                            return False

                        try:
                            EXCHANGE = ccxt.okx({
                                'apiKey': api_key,
                                'secret': secret,
                                'password': passphrase,  # OKX uses 'password' parameter for the passphrase
                                'enableRateLimit': True,
                                'options': {'defaultType': 'swap'}
                            })
                            if live_account == "N":
                                EXCHANGE.set_sandbox_mode(True)

                            # Verify the credentials work
                            EXCHANGE.fetch_balance()
                            return True
                        except Exception as e2:
                            print(f"Failed to reinitialize exchange: {e2}")
                            return False
                    else:
                        # Wait before retrying
                        delay = 1 + attempt
                        print(f"Authentication error, retrying in {delay} seconds...")
                        time.sleep(delay)
                else:
                    # For other errors on the last attempt
                    if attempt == max_retries - 1:
                        print(f"Exchange authentication error: {e}")
                        return False
                    else:
                        # Wait before retrying
                        delay = 1 + attempt
                        print(f"Error verifying credentials: {e}. Retrying in {delay} seconds...")
                        time.sleep(delay)
    except Exception as e:
        print(f"Unexpected error in ensure_exchange_credentials: {e}")
        return False

    # Default fallback
    return False


def initialize_bybit_exchange():
    """
    Initialize and return a configured Bybit exchange instance.
    Returns:
        ccxt.bybit: Configured Bybit exchange instance
    """
    # Use flat key structure directly instead of checking for nested structure
    vip_level = str(keys.get('VIP', '0'))
    api_key = keys.get('APIKEY', '')
    secret = keys.get('SECRET', '')
    live_account = keys.get('LIVE_ACCOUNT', 'N')

    rate_limit = BYBIT_RATE_LIMITS.get(vip_level, 10)  # Default to 10 if not found

    # Maximum retries for initialization with exponential backoff
    max_retries = 5

    for attempt in range(max_retries):
        try:
            EXCHANGE = ccxt.bybit({
                'apiKey': api_key,
                'secret': secret,
                'enableRateLimit': True,
                'options': {
                    'recvWindow': 10000,
                    'adjustForTimeDifference': True
                },
                'rateLimit': 1000 / rate_limit  # Convert requests/second to ms delay
            })
            if live_account == "N":
                EXCHANGE.enable_demo_trading(True)

            # Test the exchange connection to verify credentials
            try:
                EXCHANGE.fetch_balance()
                print("Successfully initialized Bybit exchange with API credentials")
                return EXCHANGE
            except Exception as e:
                error_str = str(e)
                # Check if this is a rate limit error
                if "Too Many Requests" in error_str or "rate limit" in error_str.lower() or "10006" in error_str:
                    if attempt < max_retries - 1:
                        # Calculate backoff time with jitter
                        backoff_time = exponential_backoff(attempt)
                        print(
                            f"Rate limit hit during Bybit initialization. Retry {attempt + 1}/{max_retries} after {backoff_time:.2f}s delay")
                        time.sleep(backoff_time)
                        continue
                    else:
                        print(
                            f"Warning: Rate limits hit during initialization. Will continue with limited functionality.")
                        # Return the exchange object even though we couldn't verify it completely
                        return EXCHANGE
                else:
                    # For other authentication errors, raise immediately
                    raise
        except Exception as e:
            if attempt < max_retries - 1:
                # Calculate backoff time for other errors
                backoff_time = 2 ** attempt
                print(f"Error initializing Bybit exchange: {e}")
                print(f"Retrying ({attempt + 1}/{max_retries}) in {backoff_time} seconds...")
                time.sleep(backoff_time)
            else:
                print(f"Failed to initialize Bybit exchange after {max_retries} attempts: {e}")
                print("API Key:", api_key[:5] + "..." if api_key else "Not provided")
                print("Secret:", "..." + secret[-5:] if secret else "Not provided")
                print("Live Account:", live_account)
                print("VIP Level:", vip_level)
                raise

    # If we somehow get here (shouldn't happen due to raise in the loop)
    raise Exception(f"Failed to initialize Bybit exchange after {max_retries} attempts")


def initialize_okx_exchange():
    """
    Initialize and return a configured OKX exchange instance with retry mechanism.
    Returns:
        ccxt.okx: Configured OKX exchange instance
    """
    api_key = keys.get('APIKEY', '')
    secret = keys.get('SECRET', '')
    passphrase = keys.get('PASSPHRASE', '')  # OKX requires a passphrase
    live_account = keys.get('LIVE_ACCOUNT', 'N')

    # Maximum retries for initialization
    max_retries = 5

    for attempt in range(max_retries):
        try:
            EXCHANGE = ccxt.okx({
                'apiKey': api_key,
                'secret': secret,
                'password': passphrase,  # OKX uses 'password' parameter for the passphrase
                'enableRateLimit': True,
                'rateLimit': 500,  # 500ms between requests (2 requests per second)
                'options': {
                    'defaultType': 'swap',  # Use swap for perpetual futures
                    'adjustForTimeDifference': True
                }
            })

            if live_account == "N":
                EXCHANGE.set_sandbox_mode(True)

            # Test connection with a simple API call that doesn't require auth
            # Wait with exponential backoff between retries
            if attempt > 0:
                backoff_time = exponential_backoff(attempt - 1)
                print(f"Retry attempt {attempt + 1}/{max_retries} after {backoff_time:.2f}s delay")
                time.sleep(backoff_time)

            # Use a non-authenticated endpoint to test connection
            retry_api_call(EXCHANGE.fetch_time)
            print("Successfully connected to OKX API")

            # If authentication is provided, test authenticated endpoints
            if api_key and secret and passphrase:
                try:
                    # Use retry mechanism for authenticated call
                    retry_api_call(EXCHANGE.fetch_balance)
                    print("Successfully authenticated with OKX API credentials")
                except Exception as auth_error:
                    # Log but don't fail if auth fails but connection works
                    print(f"Warning: Connected to OKX API but authentication failed: {auth_error}")
                    print("Check API Key, Secret, and Passphrase")

            return EXCHANGE

        except Exception as e:
            print(f"Attempt {attempt + 1}/{max_retries} - Error initializing OKX exchange: {e}")

            if attempt == max_retries - 1:
                # Last attempt failed, raise the exception
                print("Failed to initialize OKX exchange after multiple attempts")
                raise

            # Wait before retry with exponential backoff
            backoff_time = exponential_backoff(attempt)
            print(f"Retrying in {backoff_time:.2f} seconds...")
            time.sleep(backoff_time)


def initialize_globals():
    global keys, selected_exchange, API_KEY, config, ORDER_TYPE
    global SYMBOL, EXCHANGE, PRECISION, MIN_POS, GLASSNODE_SYMBOL, DATESTART, RUN_FREQ
    global MAX_POS, gn_data, signal_data, strategy_sr, has_run_backtesting, SHIFT
    global BYBIT_VIP_LEVEL, BYBIT_RATE_LIMIT, latest_strategy_positions
    global config_file

    # Initialize latest_strategy_positions as an empty dictionary
    latest_strategy_positions = {}

    current_dir = os.path.dirname(os.path.abspath(__file__))
    key_file = os.path.join(current_dir, 'config', 'key.yaml')
    config_file = os.path.join(current_dir, 'config', 'config_BTC.yaml')

    with open(key_file) as f:
        keys = yaml.safe_load(f)
    print('Loading key file', key_file, '.....')

    selected_exchange = keys.get('EXCHANGE').upper()
    SHIFT = int(keys.get('SHIFT', 0))  # Default to 0 if not specified

    print(f"Selected Exchange: {selected_exchange}")
    print(f"Backtesting Shift: {SHIFT}")

    # Always use flat structure for Bybit VIP level and rate limit
    if selected_exchange == "BYBIT":
        vip_level = str(keys.get('VIP', '0'))
        BYBIT_VIP_LEVEL = vip_level
        BYBIT_RATE_LIMIT = BYBIT_RATE_LIMITS.get(vip_level, 10)
        print(f"Bybit VIP Level: {BYBIT_VIP_LEVEL} - Rate Limit: {BYBIT_RATE_LIMIT} requests/second")

    # Set Glassnode API key (using flat structure)
    API_KEY = keys.get('GLASSNODE_API', '')

    with open(config_file) as f:
        config = yaml.safe_load(f)
    print('Loading config file', config_file, '.....')

    ORDER_TYPE = config.get('ORDER_TYPE')

    # Use the single unified symbol field from config
    SYMBOL = config['ASSET'].get('symbol', '')

    if selected_exchange == "BYBIT":
        # Initialize Bybit exchange just once here
        EXCHANGE = initialize_bybit_exchange()

        # Add retry logic for load_markets with exponential backoff
        max_retries = 5
        for attempt in range(max_retries):
            try:
                EXCHANGE.load_markets()
                break
            except Exception as e:
                error_str = str(e)
                # Check if this is a rate limit error
                if "Too Many Requests" in error_str or "rate limit" in error_str.lower() or "10006" in error_str:
                    if attempt < max_retries - 1:
                        # Calculate backoff time with jitter
                        backoff_time = exponential_backoff(attempt)
                        print(
                            f"Rate limit hit during market loading. Retry {attempt + 1}/{max_retries} after {backoff_time:.2f}s delay")
                        time.sleep(backoff_time)
                    else:
                        print(
                            f"Warning: Rate limits hit during market loading. Will continue with limited functionality.")
                else:
                    # For other errors, raise immediately
                    raise

        try:
            market = EXCHANGE.market(SYMBOL)
            PRECISION = int(abs(math.log10(market['precision']['amount'])))
            MIN_POS = market['limits']['amount']['min']
        except Exception as e:
            print(f"Error getting market info for {SYMBOL}: {e}")
            print("Using default precision and minimum position values")
            PRECISION = 4  # Default precision
            MIN_POS = 0.001  # Default minimum position

    elif selected_exchange == "BINANCE":
        # Always use flat structure for Binance too
        api_key = keys.get('APIKEY', '')
        secret = keys.get('SECRET', '')
        live_account = keys.get('LIVE_ACCOUNT', 'N')

        EXCHANGE = ccxt.binance({
            'apiKey': api_key,
            'secret': secret,
            'enableRateLimit': True,
            'options': {'defaultType': 'future'}
        })
        if live_account == "N":
            EXCHANGE.set_sandbox_mode(True)
        EXCHANGE.load_markets()
        market = EXCHANGE.market(SYMBOL)
        PRECISION = int(abs(math.log10(market['precision']['amount'])))
        MIN_POS = market['limits']['amount']['min']
    elif selected_exchange == "OKX":
        # Initialize OKX exchange
        api_key = keys.get('APIKEY', '')
        secret = keys.get('SECRET', '')
        passphrase = keys.get('PASSPHRASE', '')  # OKX requires a passphrase
        live_account = keys.get('LIVE_ACCOUNT', 'N')

        print(f"Initializing OKX exchange with API credentials. Passphrase provided: {'Yes' if passphrase else 'No'}")
        if not passphrase:
            print("WARNING: OKX requires a passphrase for authenticated API calls")

        EXCHANGE = initialize_okx_exchange()
        EXCHANGE.load_markets()
        market = EXCHANGE.market(SYMBOL)
        PRECISION = int(abs(math.log10(market['precision']['amount'])))
        MIN_POS = market['limits']['amount']['min']

        # Verify credentials work for authenticated API calls
        if api_key and secret and passphrase:
            print("Verifying OKX exchange credentials...")
            if ensure_exchange_credentials():
                print("OKX credentials verified successfully")
            else:
                print("WARNING: OKX credentials may not be valid for authenticated API calls")
    else:
        raise ValueError(f"Unsupported exchange: {selected_exchange}")

    GLASSNODE_SYMBOL = config['ASSET']['glassnode_symbol']
    DATESTART = config['ASSET']['since']
    RUN_FREQ = config.get('RUN_FREQ', 10)

    print('Symbol:', SYMBOL)
    print('Glassnode price:', GLASSNODE_SYMBOL)
    print('Since:', DATESTART)
    print('Precision:', PRECISION)
    print('Minimum Position:', MIN_POS)

    # Make sure data directory and required files exist
    data_dir = ensure_data_directory()

    # Ensure max_positions.csv exists, creating it with defaults if needed
    max_positions_file = ensure_max_positions_file()

    # Ensure strategy_performance.csv exists, creating it with defaults if needed
    ensure_strategy_performance_file()

    try:
        max_positions_df = pd.read_csv(max_positions_file)
        symbol_row = max_positions_df.loc[max_positions_df['symbol'] == SYMBOL]

        if len(symbol_row) == 0:
            # Add this symbol to max_positions.csv if it doesn't exist
            print(f"Symbol {SYMBOL} not found in max_positions.csv. Adding with default values.")
            new_row = pd.DataFrame({
                'symbol': [SYMBOL],
                'strategy_count': [len(config['STRATEGIES'])],
                'weight': [1.0],
                'fund_allocation': [100000.0],
                'max_pos': [1.0]
            })
            max_positions_df = pd.concat([max_positions_df, new_row], ignore_index=True)
            max_positions_df.to_csv(max_positions_file, index=False)
            MAX_POS = 1.0
        else:
            MAX_POS = symbol_row['max_pos'].values[0]

        print('Max. Position:', MAX_POS)
    except Exception as e:
        print(f"Warning: Error while reading max positions: {e}")
        print("Setting MAX_POS to default value of 1.0")
        MAX_POS = 1.0

    for strategy in config['STRATEGIES']:
        print(f"\nStrategy Name: {strategy['name']}")
        for key, value in strategy.items():
            if key not in ['name', 'ratio']:
                print(f"  {key.capitalize()}: {value}")

    gn_data = {strategy['name']: pd.DataFrame(columns=['t', 'value', 'price']) for strategy in config['STRATEGIES']}
    signal_data = pd.DataFrame(columns=['dt', 'pos'])
    strategy_sr = {}
    has_run_backtesting = False  # Flag to track if backtesting has been run

    pd.set_option('display.max_rows', None)
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', 1000)


def backtesting(df, strategy):
    global has_run_backtesting
    resolution_multipliers = {
        '1d': 365,  # 365 periods per year
        '24h': 365,  # 365 periods per year
        '12h': 365 * 2,  # 730 periods per year
        '6h': 365 * 4,  # 1460 periods per year
        '4h': 365 * 6,  # 2190 periods per year
        '2h': 365 * 12,  # 4380 periods per year
        '1h': 365 * 24,  # 8760 periods per year
        '15m': 365 * 24 * 4,  # 35040 periods per year
        '10m': 365 * 24 * 6,  # 52560 periods per year
        '5m': 365 * 24 * 12,  # 105120 periods per year
        '3m': 365 * 24 * 20,  # 175200 periods per year
        '1m': 365 * 24 * 60  # 525600 periods per year
    }
    time_variable = resolution_multipliers.get(strategy['resolution'], 0)
    strategy_name = strategy['name']

    df['chg'] = df['price'].pct_change()
    df['pos_t-1'] = df['pos'].shift(1)
    df['trade'] = (df['pos_t-1'] != df['pos']).astype(int)
    # Set first row trade to 0 since there's no actual trade on the first day
    if len(df) > 0:
        df.loc[df.index[0], 'trade'] = 0
    df['pnl'] = df['pos_t-1'] * df['chg'] - df['trade'] * 0.05 / 100
    df['cumu'] = df['pnl'].cumsum()
    df['dd'] = df['cumu'].cummax() - df['cumu']
    ar = round(df['pnl'].mean() * time_variable, 3)
    sr = round(df['pnl'].mean() / df['pnl'].std() * np.sqrt(time_variable), 3) if df['pnl'].std() != 0 else 0
    mdd = round(df['dd'].max(), 3)
    cr = round(ar / mdd, 3) if mdd != 0 else 0
    pos = df['pos'].iloc[-1]
    bt_result = pd.Series(
        [sr, cr, ar, mdd, pos],
        index=['SR', 'CR', 'AR', 'MDD', 'POS']
    )
    print(bt_result.to_frame().T)

    # Store SR and POS for weighting and display
    strategy_sr[strategy_name] = {'sr': sr, 'pos': pos}

    # Mark that backtesting has been run
    has_run_backtesting = True

    # Update strategy performance metrics in CSV using safe update mechanism
    data_dir = ensure_data_directory()
    performance_file = os.path.join(data_dir, 'strategy_performance.csv')

    # Only update if the performance file exists, never create a new one
    if os.path.exists(performance_file):
        # Check and repair weight_ratios before updating
        if not check_and_repair_weight_ratios(performance_file):
            print(f"Warning: Could not verify weight_ratio integrity for {performance_file}")

        # Prepare updates dictionary (excluding weight_ratio to protect it)
        updates = {
            'SR': sr,
            'AR': ar,
            'CR': cr,
            'MDD': mdd,
            'POS': pos,
            'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'Updated': True
        }

        # Use safe update mechanism
        success = safe_update_strategy_performance(performance_file, strategy_name, updates)

        if not success:
            print(f"Failed to safely update strategy '{strategy_name}' performance metrics")
            # Send alert about update failure
            try:
                send_tg(f"⚠️ WARNING: {SYMBOL} failed to update strategy '{strategy_name}' performance metrics safely")
            except:
                print("Could not send update failure alert via Telegram")
    else:
        print(
            f"Warning: Performance file {performance_file} does not exist. The file must be created by Rebalance.py first.")

    # Save backtest results to CSV in data directory
    resolution = strategy['resolution']
    csv_filename = os.path.join(data_dir, f'{resolution}_{strategy_name}.csv')
    df.to_csv(csv_filename, index=True)
    print(f"Backtest results saved to {csv_filename}")

    return df['pos'].iloc[-1]


def apply_strategy(df, strategy):
    if df.empty:
        return 0

    strategy_name = strategy['name']
    print(f"Strategy {strategy_name}:")

    try:
        # Dynamically get the strategy method from the Strategy class
        strategy_method = getattr(Strategy, strategy['model'])
    except AttributeError:
        print(f"Unknown strategy model: {strategy['model']}")
        return 0

    df['pos'] = strategy_method(
        df, strategy.get('x'), strategy.get('y'), strategy.get('type'), strategy.get('style')
    )

    # Always run backtesting when apply_strategy is called
    backtesting(df, strategy)

    return df['pos'].iloc[-1]


def process_glassnode_data(data, metric_key=None):
    result = []
    if not data or not isinstance(data, list):
        print(f"Invalid Glassnode data format received: {data[:100] if data else 'None'}")
        return pd.DataFrame(columns=['t', 'value'])

    # Convert metric_key to string if it's not None
    if metric_key is not None:
        metric_key = str(metric_key)

    for item in data:
        try:
            if not isinstance(item, dict):
                continue

            value = None
            if 'v' in item:
                value = item['v']
            elif 'o' in item:
                if metric_key and isinstance(item['o'], dict):
                    value = item['o'].get(metric_key)
                elif isinstance(item['o'], (int, float)):
                    value = item['o']
                elif isinstance(item['o'], dict):
                    value = sum(item['o'].values())

            if value is not None and 't' in item:
                result.append({
                    't': pd.to_datetime(item['t'], unit='s'),
                    'value': value
                })
        except Exception as e:
            print(f"Error processing Glassnode data item: {e}")
            continue

    return pd.DataFrame(result)


def process_cex_data(data, data_type='close'):
    """
    Process CEX (Bybit) data into a DataFrame.

    Args:
        data (list or DataFrame): List of OHLCV data points or DataFrame with OHLCV data
        data_type (str): Type of data to extract ('open', 'high', 'low', 'close', 'volume')

    Returns:
        pd.DataFrame: Processed DataFrame with timestamp and value column
    """
    if data is None or (isinstance(data, list) and len(data) == 0) or (isinstance(data, pd.DataFrame) and data.empty):
        return pd.DataFrame({'t': [], 'value': []})

    try:
        # Check if input is already a DataFrame
        if isinstance(data, pd.DataFrame):
            df = data

            # Verify necessary columns exist
            required_cols = ['timestamp', data_type]
            if not all(col in df.columns for col in required_cols):
                print(f"Warning: Required columns {required_cols} not found in DataFrame")
                if 'timestamp' not in df.columns and 't' in df.columns:
                    # Try to use 't' column as timestamp if available
                    df['timestamp'] = df['t']

                # If data_type not found, try to use 'close' as fallback
                if data_type not in df.columns and 'close' in df.columns:
                    print(f"Data type '{data_type}' not found, using 'close' instead")
                    data_type = 'close'
                else:
                    # Can't proceed without required data
                    raise ValueError(f"Required column '{data_type}' not found in DataFrame")
        else:
            # Convert list to DataFrame - Bybit OHLCV format: [timestamp, open, high, low, close, volume]
            df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

        # Convert timestamp to datetime if needed
        if 't' not in df.columns:
            if pd.api.types.is_numeric_dtype(df['timestamp']):
                # Check timestamp scale (seconds vs milliseconds)
                if df['timestamp'].iloc[0] > 9999999999:  # Timestamp in milliseconds
                    df['t'] = pd.to_datetime(df['timestamp'], unit='ms')
                else:  # Timestamp in seconds
                    df['t'] = pd.to_datetime(df['timestamp'], unit='s')
            else:
                # Try to parse as datetime string
                df['t'] = pd.to_datetime(df['timestamp'])

        # Extract the requested data type
        if data_type not in df.columns:
            print(f"Warning: Invalid data_type '{data_type}', using 'close' instead")
            data_type = 'close'

        # Create new DataFrame with timestamp and selected value
        df_processed = pd.DataFrame({
            't': df['t'] if 't' in df.columns else pd.to_datetime(df['timestamp'], unit='ms'),
            'value': df[data_type]
        })

        # Sort by timestamp
        df_processed = df_processed.sort_values('t')

        return df_processed

    except Exception as e:
        print(f"Error processing CEX data: {str(e)}")
        print(f"Data type: {type(data)}")
        if isinstance(data, pd.DataFrame):
            print(f"DataFrame columns: {data.columns.tolist()}")
        return pd.DataFrame({'t': [], 'value': []})


# ===== Data Fetching Functions =====
def prepare_strategy_with_globals(strategy):
    """
    Prepare strategy data with all required global variables for multiprocessing.
    This ensures that child processes have access to all necessary data.
    """
    return {
        'strategy': strategy,
        'since': DATESTART,
        'api_key': API_KEY,
        'symbol': SYMBOL,
        'glassnode_symbol': GLASSNODE_SYMBOL,
        'exchange': selected_exchange,
        'exchange_obj': EXCHANGE,  # Pass the exchange object as well
        'keys': keys  # Pass the keys variable for Bybit VIP level access
    }


def find_oldest_since_date():
    """Find the oldest 'since' date, prioritizing the main config file before checking others."""
    # Access the global config_file variable defined in initialize_globals
    global config_file

    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_dir = os.path.join(current_dir, 'config')

    # Use the global config_file variable
    main_config_basename = os.path.basename(config_file)

    # First, try to get the 'since' value from the main config file
    if os.path.exists(config_file):
        try:
            with open(config_file) as f:
                main_config = yaml.safe_load(f)

            if 'ASSET' in main_config and 'since' in main_config['ASSET']:
                main_since = int(main_config['ASSET']['since'])
                print(f"Using 'since' value from {main_config_basename}: {datetime.datetime.fromtimestamp(main_since)}")
                return main_since
        except Exception as e:
            print(f"Error reading {main_config_basename}: {e}")

    # If main config doesn't have a 'since' value, check all other config files
    oldest_since = None
    config_files = [f for f in os.listdir(config_dir) if f.startswith('config_') and f.endswith('.yaml')]

    for config_file_name in config_files:
        # Skip main config as we already checked it
        if config_file_name == main_config_basename:
            continue

        config_path = os.path.join(config_dir, config_file_name)
        try:
            with open(config_path) as f:
                config_data = yaml.safe_load(f)

            if 'ASSET' in config_data and 'since' in config_data['ASSET']:
                since_value = int(config_data['ASSET']['since'])
                if oldest_since is None or since_value < oldest_since:
                    oldest_since = since_value
        except Exception as e:
            print(f"Error reading 'since' date from {config_file_name}: {e}")

    if oldest_since is None:
        # Use a default of 3 years as fallback if no since date is found
        default_years = 3
        end_time = int(time.time())
        oldest_since = end_time - (default_years * 365 * 24 * 60 * 60)
        print(f"No 'since' dates found in config files, using {default_years} years as fallback")
    else:
        print(f"Using oldest 'since' date from config files: {datetime.datetime.fromtimestamp(oldest_since)}")

    return oldest_since


def fetch_data_mp(strategy_with_globals):
    """
    Fetch data for a strategy with all required global variables passed as parameters.
    This avoids issues with multiprocessing not sharing global variables.
    """
    # Unpack the strategy and global variables
    strategy = strategy_with_globals['strategy']
    since = strategy_with_globals['since']
    api_key = strategy_with_globals['api_key']
    symbol = strategy_with_globals['symbol']
    glassnode_symbol = strategy_with_globals['glassnode_symbol']
    exchange = strategy_with_globals['exchange']
    EXCHANGE = strategy_with_globals['exchange_obj']
    keys = strategy_with_globals['keys']  # Get the keys variable for VIP level access

    # Add random delay before processing to avoid rate limits
    random_delay = random.randint(STRATEGY_MIN_DELAY, STRATEGY_MAX_DELAY)  # Random delay between configured values
    print(f"Adding random delay of {random_delay} seconds before processing {strategy['name']} to avoid rate limits")
    time.sleep(random_delay)
    until = int(time.time())
    strategy_name = strategy['name']  # Define strategy_name at the beginning of the function
    print(
        f"Fetching Data for {strategy_name} using multiprocessing: {strategy['api']}, {strategy['api_symbol']}, {strategy['resolution']}")

    try:
        if strategy['api'] in ["cex", "bybit"]:  # Support both "cex" and "bybit" API types
            # Check if exchange is Bybit - only Bybit supports CEX directly
            if exchange != "BYBIT":
                # For exchanges other than Bybit, skip data fetching for CEX/Bybit strategies
                warning_msg = f"⚠️ WARNING: Exchange {exchange} does not support strategy['api'] == '{strategy['api']}' for {strategy['name']}. Using fallback data."
                print(warning_msg)

                # Send Telegram notification about unsupported exchange for this strategy
                send_tg(f"{warning_msg} Strategy will continue with placeholder data.")

                # Create a minimal fallback DataFrame with basic structure
                # This will allow backtesting to continue with previous data if available
                if strategy_name in gn_data and not gn_data[strategy_name].empty:
                    # Use existing data if available (keep using cached data)
                    print(f"Using existing cached data for {strategy['name']}")
                    return {"strategy_name": strategy['name'], "df": gn_data[strategy_name]}
                else:
                    # Create minimal fallback data with current timestamp
                    current_time = pd.Timestamp.now()
                    fallback_df = pd.DataFrame({
                        't': [current_time],
                        'value': [1.0],  # Placeholder value
                        'price': [1.0]  # Placeholder price
                    })
                    print(f"Created fallback data for {strategy['name']}")
                    return {"strategy_name": strategy['name'], "df": fallback_df}

            # For Bybit, continue with normal CEX/Bybit data fetching
            # Don't reinitialize EXCHANGE here, use the global one
            # Check if this is initial load or scheduled update
            # resolution: 1m,3m,5m,15m,30m,1h,2h,4h,6h,12h,1d,1w,1m

            # Create cex_data directory if it doesn't exist
            cex_data_dir = ensure_cex_data_directory()

            # Define the CSV file path for caching - use api_symbol from strategy config
            csv_filename = os.path.join(cex_data_dir,
                                        f"{strategy['api']}_data_{strategy_name}_{strategy['resolution']}.csv")

            print(f"Using API symbol: {strategy.get('api_symbol', symbol)} for {strategy['api']} data fetching")

            # Use api_symbol from strategy if available, otherwise fall back to main symbol
            trading_symbol = strategy.get('api_symbol', symbol)

            # Check if we have existing cached data
            if os.path.exists(csv_filename):
                print(f"Found cached {strategy['api'].upper()} data for {strategy_name} at {csv_filename}")
                try:
                    # Load the cached data
                    cached_df = pd.read_csv(csv_filename)

                    # Convert timestamp to datetime for proper comparison
                    if 'timestamp' in cached_df.columns:
                        cached_df['timestamp'] = pd.to_numeric(cached_df['timestamp'])

                    if 't' in cached_df.columns:
                        cached_df['t'] = pd.to_datetime(cached_df['t'])

                    if not cached_df.empty:
                        # Get the latest timestamp from the cached data
                        latest_timestamp = cached_df['timestamp'].max()
                        print(f"Latest cached timestamp: {datetime.datetime.fromtimestamp(latest_timestamp / 1000)}")

                        # Fetch only the new data since the latest timestamp
                        end_time = int(time.time() * 1000)  # Current time in milliseconds

                        # Add a small buffer to avoid duplicate entries (add 1ms)
                        start_time = int(latest_timestamp) + 1

                        print(
                            f"Fetching incremental data from {datetime.datetime.fromtimestamp(start_time / 1000)} to {datetime.datetime.fromtimestamp(end_time / 1000)}")

                        try:
                            # Get VIP-based rate limits for Bybit
                            vip_level = str(keys.get('VIP', '0'))
                            sleep_time = 1 / BYBIT_RATE_LIMITS.get(vip_level, 10)

                            # Fetch new data using api_symbol
                            new_ohlcv = EXCHANGE.fetch_ohlcv(
                                trading_symbol,
                                strategy['resolution'],
                                since=start_time,
                                limit=1000
                            )

                            # Small delay to respect rate limits
                            time.sleep(sleep_time)

                            if new_ohlcv and len(new_ohlcv) > 0:
                                print(f"Fetched {len(new_ohlcv)} new data points")

                                # Convert new data to DataFrame
                                new_df = pd.DataFrame(new_ohlcv,
                                                      columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                                new_df['t'] = pd.to_datetime(new_df['timestamp'], unit='ms')

                                # Merge with existing data
                                combined_df = pd.concat([cached_df, new_df], ignore_index=True)

                                # Remove duplicates based on timestamp
                                combined_df = combined_df.drop_duplicates(subset=['timestamp'])

                                # Sort by timestamp
                                combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)

                                # Save the updated data back to CSV
                                combined_df.to_csv(csv_filename, index=False)
                                print(f"Updated cached data saved to {csv_filename}")

                                # Create value and price dataframes as before
                                df_value = pd.DataFrame({
                                    't': combined_df['t'],
                                    'value': combined_df['close']
                                })

                                df_price = pd.DataFrame({
                                    't': combined_df['t'],
                                    'value': combined_df['close']
                                })

                                # Merge the dataframes
                                merged_df = pd.merge(df_value, df_price, how='inner', on='t')
                                merged_df = merged_df.rename(columns={'value_x': 'value', 'value_y': 'price'})
                                df = merged_df

                                print(f"Data range: {df['t'].min()} to {df['t'].max()}")
                                return {"strategy_name": strategy['name'], "df": df}
                            else:
                                print("No new data available since last update")

                                # Create dataframes from the cached data
                                df_value = pd.DataFrame({
                                    't': cached_df['t'],
                                    'value': cached_df['close']
                                })

                                df_price = pd.DataFrame({
                                    't': cached_df['t'],
                                    'value': cached_df['close']
                                })

                                # Merge the dataframes
                                merged_df = pd.merge(df_value, df_price, how='inner', on='t')
                                merged_df = merged_df.rename(columns={'value_x': 'value', 'value_y': 'price'})
                                df = merged_df

                                print(f"Using existing cached data: {df['t'].min()} to {df['t'].max()}")
                                return {"strategy_name": strategy['name'], "df": df}
                        except Exception as e:
                            print(f"Error fetching incremental data: {e}")
                            print("Falling back to cached data")

                            # Create dataframes from the cached data
                            df_value = pd.DataFrame({
                                't': cached_df['t'],
                                'value': cached_df['close']
                            })

                            df_price = pd.DataFrame({
                                't': cached_df['t'],
                                'value': cached_df['close']
                            })

                            # Merge the dataframes
                            merged_df = pd.merge(df_value, df_price, how='inner', on='t')
                            merged_df = merged_df.rename(columns={'value_x': 'value', 'value_y': 'price'})
                            df = merged_df

                            print(f"Using existing cached data: {df['t'].min()} to {df['t'].max()}")
                            return {"strategy_name": strategy['name'], "df": df}
                except Exception as e:
                    print(f"Error reading cached data: {e}")
                    print("Will fetch fresh data")

            # If we reach here, either there's no cached data or we couldn't read it
            # Fetch all historical data from scratch
            print(f"Fetching full historical {strategy['api'].upper()} data from exchange")

            # Fetch historical data starting from the 'since' date in config
            end_time = int(time.time() * 1000)  # Current time in milliseconds
            start_time = since * 1000  # Convert to milliseconds

            print(
                f"Fetching historical data from {datetime.datetime.fromtimestamp(start_time / 1000)} to {datetime.datetime.fromtimestamp(end_time / 1000)}")

            # Initialize empty list to store all OHLCV data
            all_ohlcv = []

            # For Bybit, use the standard approach
            current_start = start_time
            chunk_count = 0

            # Get VIP-based rate limits for Bybit
            vip_level = str(keys.get('VIP', '0'))
            sleep_time = 1 / BYBIT_RATE_LIMITS.get(vip_level, 10)

            # Print clear log about the start time
            start_date = datetime.datetime.fromtimestamp(start_time / 1000)
            end_date = datetime.datetime.fromtimestamp(end_time / 1000)
            print(f"Fetching data from {start_date} to {end_date} in forward chronological order")

            # Track progress
            total_duration_ms = end_time - start_time

            while current_start < end_time:
                try:
                    # Calculate progress percentage
                    progress = min(100, ((current_start - start_time) / total_duration_ms) * 100)
                    if chunk_count % 10 == 0:  # Show progress every 10 chunks
                        print(
                            f"Progress: {progress:.1f}% - Currently at {datetime.datetime.fromtimestamp(current_start / 1000)}")

                    # Add rate limit handling
                    if chunk_count % 5 == 0:  # Add extra sleep every 5 chunks
                        time.sleep(sleep_time * 2)  # Double the sleep time for extra caution

                    # Regular fetch for Bybit using api_symbol
                    chunk = EXCHANGE.fetch_ohlcv(
                        trading_symbol,
                        strategy['resolution'],
                        since=int(current_start),  # Ensure integer timestamp
                        limit=1000
                    )

                    # Regular sleep after each request
                    time.sleep(sleep_time)

                    if not chunk or len(chunk) == 0:
                        print(
                            f"No data returned for chunk starting at {datetime.datetime.fromtimestamp(current_start / 1000)}")
                        break

                    all_ohlcv.extend(chunk)
                    current_start = chunk[-1][0] + 1  # Start from next timestamp after last data point

                    # Increment chunk counter for rate limiting
                    chunk_count += 1

                    # Print progress for large data fetches
                    if chunk_count % 5 == 0:
                        print(f"Fetched {chunk_count} chunks of data ({len(all_ohlcv)} data points)")

                except Exception as e:
                    error_msg = str(e)
                    print(f"Error fetching data chunk: {error_msg}")

                    # Handle rate limiting errors specifically
                    if "Too Many Requests" in error_msg or "rate limit" in error_msg.lower():
                        retry_delay = 10  # 10 seconds for rate limit errors
                        print(f"Rate limit hit. Sleeping for {retry_delay} seconds before retrying")
                        time.sleep(retry_delay)
                        # Don't increment current_start to retry the same chunk
                    else:
                        # For other errors, wait a bit and continue with next chunk
                        time.sleep(1)
                        if len(all_ohlcv) > 0:
                            # If we have some data already, continue from last successful point
                            print("Continuing from last successful data point")
                            current_start = all_ohlcv[-1][0] + 1
                        else:
                            # If no data fetched yet, try a different approach - fetch from a more recent time
                            print("Trying a more recent time range")
                            current_start = int((end_time - start_time) * 0.5 + start_time)  # Try from halfway point

            if not all_ohlcv:
                print(f"Error fetching OHLCV data from {exchange} for {strategy['name']}")
                return {"strategy_name": strategy['name'], "df": pd.DataFrame()}

            # Create dataframe from historical OHLCV data
            df = pd.DataFrame(all_ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['t'] = pd.to_datetime(df['timestamp'], unit='ms')

            # Save the fetched data to CSV for future use
            df.to_csv(csv_filename, index=False)
            print(f"Saved {len(df)} data points to {csv_filename}")

            # Print summary
            print(f"Successfully loaded {len(df)} data points for {strategy['name']}, " +
                  f"from {df['t'].min()} to {df['t'].max()}")

            # Create value dataframe (using close price as the value)
            df_value = pd.DataFrame({
                't': df['t'],
                'value': df['close']
            })

            # Create price dataframe (also using close price)
            df_price = pd.DataFrame({
                't': df['t'],
                'value': df['close']
            })

            # Merge the dataframes
            merged_df = pd.merge(df_value, df_price, how='inner', on='t')
            merged_df = merged_df.rename(columns={'value_x': 'value', 'value_y': 'price'})
            df = merged_df

            print(f"Data range: {df['t'].min()} to {df['t'].max()}")
            return {"strategy_name": strategy['name'], "df": df}

        else:
            # Fetch Glassnode data with improved retry handling
            try:
                # Use the retry_glassnode_request function for the value endpoint
                res_value = retry_glassnode_request(
                    strategy['api'],
                    params={
                        "a": strategy['api_symbol'],
                        "s": since,
                        "u": until,
                        "api_key": api_key,
                        "i": strategy['resolution']
                    }
                )

                # Process the response
                value_json = res_value.json()
                df_value = process_glassnode_data(value_json, metric_key=strategy['metric_key'])

                # Use the retry_glassnode_request function for the price endpoint
                res_price = retry_glassnode_request(
                    "https://api.glassnode.com/v1/metrics/market/price_usd_close",
                    params={
                        "a": glassnode_symbol,
                        "s": since,
                        "u": until,
                        "api_key": api_key,
                        "i": strategy['resolution']
                    }
                )

                # Process the response
                price_json = res_price.json()
                df_price = process_glassnode_data(price_json)

                # Merge the dataframes
                df = pd.merge(df_value, df_price, how='inner', on='t')
                df = df.rename(columns={'value_x': 'value', 'value_y': 'price'})

            except Exception as e:
                print(f"Error in Glassnode API requests for {strategy['name']}: {e}")
                # Return empty dataframe on error
                return {"strategy_name": strategy['name'], "df": pd.DataFrame()}

        warnings.filterwarnings(
            "ignore",
            category=FutureWarning,
            message="The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. *"
        )
        return {"strategy_name": strategy['name'], "df": df}
    except Exception as e:
        print(f"Error in fetch_data_mp for {strategy['name']}: {e}")
        return {"strategy_name": strategy['name'], "df": pd.DataFrame()}


def classify_order_size(symbol, quantity):
    """
    Classify order as small or large based on quantity and USD value.

    Args:
        symbol (str): Trading symbol
        quantity (float): Order quantity in asset units

    Returns:
        tuple: (is_large_order, usd_value, classification_reason)
    """
    global EXCHANGE

    try:
        # Get current price to calculate USD value
        ticker = EXCHANGE.fetch_ticker(symbol)
        current_price = ticker['last']
        usd_value = abs(quantity) * current_price

        # Check both quantity and USD thresholds
        quantity_large = abs(quantity) >= LARGE_ORDER_THRESHOLD
        usd_large = usd_value >= LARGE_ORDER_USD_THRESHOLD

        is_large = quantity_large or usd_large

        if is_large:
            reason = f"Quantity: {abs(quantity):.4f} >= {LARGE_ORDER_THRESHOLD}" if quantity_large else f"USD: ${usd_value:,.2f} >= ${LARGE_ORDER_USD_THRESHOLD:,.2f}"
        else:
            reason = f"Small order - Quantity: {abs(quantity):.4f}, USD: ${usd_value:,.2f}"

        return is_large, usd_value, reason

    except Exception as e:
        print(f"Error classifying order size: {e}")
        # Default to small order if we can't classify
        return False, 0, "Classification failed - defaulting to small order"


def execute_small_order(symbol, side, quantity):
    """
    Execute small orders with aggressive settings for fast fills.

    Args:
        symbol (str): Trading symbol
        side (str): 'buy' or 'sell'
        quantity (float): Order quantity

    Returns:
        Order result or None
    """
    print(f"🚀 SMALL ORDER EXECUTION: {side} {quantity} {symbol}")
    print(
        f"   Settings: {SMALL_ORDER_TIMEOUT}s timeout, {SMALL_ORDER_SPREAD * 100:.3f}% spread, {SMALL_ORDER_CHECK_INTERVAL}s intervals")

    start_time = time.time()

    result = execute_order_with_params(
        symbol=symbol,
        side=side,
        quantity=quantity,
        timeout_seconds=SMALL_ORDER_TIMEOUT,
        spread_percentage=SMALL_ORDER_SPREAD,
        check_interval=SMALL_ORDER_CHECK_INTERVAL,
        max_attempts=SMALL_ORDER_MAX_ATTEMPTS,
        order_type="small"
    )

    execution_time = time.time() - start_time

    # Calculate and send metrics for small orders
    if result:
        print(f"   📈 Calculating small order metrics...")
        metrics = calculate_order_metrics([result], symbol, quantity, side)

        if metrics:
            print(f"   📊 Small order execution time: {execution_time:.1f}s")
            print(f"   📊 Sending metrics to Telegram...")
            send_order_completion_telegram(symbol, side, quantity, metrics, execution_time)

    return result


def execute_large_order(symbol, side, quantity):
    """
    Execute large orders with patient settings and optional chunking.

    Args:
        symbol (str): Trading symbol
        side (str): 'buy' or 'sell'
        quantity (float): Order quantity

    Returns:
        Order result or None
    """
    print(f"🐋 LARGE ORDER EXECUTION: {side} {quantity} {symbol}")
    print(
        f"   Settings: {LARGE_ORDER_TIMEOUT}s timeout, {LARGE_ORDER_SPREAD * 100:.3f}% spread, {LARGE_ORDER_CHECK_INTERVAL}s intervals")

    # Check if order should be chunked based on USDT value and dynamic sizing
    try:
        ticker = EXCHANGE.fetch_ticker(symbol)
        current_price = ticker['last']
        order_usdt_value = abs(quantity) * current_price

        # Calculate what the chunk size would be for this order
        chunk_size, num_chunks, chunk_usdt_value = calculate_dynamic_chunk_size(symbol, quantity, current_price)

        # Chunk if order would result in more than 1 chunk OR if order value is very large
        should_chunk = (num_chunks > 1) or (order_usdt_value > MAX_CHUNK_USDT_VALUE)

        if should_chunk:
            print(f"   📦 Large order will be executed in {num_chunks} dynamic chunks")
            print(
                f"   💰 Order value: ${order_usdt_value:,.2f} USDT → {num_chunks} chunks of ~${chunk_usdt_value:.2f} USDT each")
            return execute_chunked_order(symbol, side, quantity)
        else:
            print(f"   📋 Large order executed as single order (${order_usdt_value:,.2f} USDT - no chunking needed)")

    except Exception as e:
        print(f"   ⚠️  Error in chunking analysis: {e}")
        print(f"   📋 Falling back to single order execution")

    start_time = time.time()

    result = execute_order_with_params(
        symbol=symbol,
        side=side,
        quantity=quantity,
        timeout_seconds=LARGE_ORDER_TIMEOUT,
        spread_percentage=LARGE_ORDER_SPREAD,
        check_interval=LARGE_ORDER_CHECK_INTERVAL,
        max_attempts=LARGE_ORDER_MAX_ATTEMPTS,
        order_type="large"
    )

    execution_time = time.time() - start_time

    # Calculate and send metrics for single large orders
    if result:
        print(f"   📈 Calculating large order metrics...")
        metrics = calculate_order_metrics([result], symbol, quantity, side)

        if metrics:
            print(f"   📊 Large order execution time: {execution_time:.1f}s")
            print(f"   📊 Sending metrics to Telegram...")
            send_order_completion_telegram(symbol, side, quantity, metrics, execution_time)

    return result


def calculate_order_metrics(order_results, symbol, original_quantity, side):
    """
    Calculate slippage and fee metrics from order results.

    Args:
        order_results (list): List of successful order results
        symbol (str): Trading symbol
        original_quantity (float): Original order quantity
        side (str): Order side ('buy' or 'sell')

    Returns:
        dict: Metrics including slippage, fees, execution summary
    """
    try:
        if not order_results:
            return None

        total_filled = 0
        total_cost = 0
        total_fees = 0
        prices = []

        # Get current market price for slippage calculation
        ticker = EXCHANGE.fetch_ticker(symbol)
        current_market_price = ticker['last']

        for order in order_results:
            if order and order.get('filled', 0) > 0:
                filled = float(order.get('filled', 0))
                avg_price = float(order.get('average', 0)) if order.get('average') else float(order.get('price', 0))
                fee = float(order.get('fee', {}).get('cost', 0)) if order.get('fee') else 0

                total_filled += filled
                total_cost += filled * avg_price
                total_fees += fee
                prices.append(avg_price)

        if total_filled == 0:
            return None

        # Calculate average execution price
        avg_execution_price = total_cost / total_filled

        # Calculate slippage
        if side.lower() == 'buy':
            slippage_bps = ((avg_execution_price - current_market_price) / current_market_price) * 10000
        else:
            slippage_bps = ((current_market_price - avg_execution_price) / current_market_price) * 10000

        # Calculate fee rate
        fee_rate_bps = (total_fees / total_cost) * 10000 if total_cost > 0 else 0

        # Fill rate
        fill_rate = (total_filled / abs(original_quantity)) * 100

        metrics = {
            'total_filled': total_filled,
            'avg_execution_price': avg_execution_price,
            'current_market_price': current_market_price,
            'slippage_bps': slippage_bps,
            'total_fees': total_fees,
            'fee_rate_bps': fee_rate_bps,
            'fill_rate': fill_rate,
            'total_cost': total_cost,
            'chunk_count': len([o for o in order_results if o and o.get('filled', 0) > 0]),
            'price_range': {'min': min(prices), 'max': max(prices)} if prices else None
        }

        return metrics

    except Exception as e:
        print(f"Error calculating order metrics: {e}")
        return None


def send_order_completion_telegram(symbol, side, quantity, metrics, execution_time=None):
    """
    Send detailed order completion notification to Telegram.

    Args:
        symbol (str): Trading symbol
        side (str): Order side
        quantity (float): Original order quantity
        metrics (dict): Order execution metrics
        execution_time (float): Total execution time in seconds
    """
    try:
        if not metrics:
            send_tg(f"✅ {symbol} {side.upper()} order completed: {abs(quantity)} (metrics unavailable)")
            return

        direction = "BUY" if side.lower() == 'buy' else "SELL"
        is_chunked = metrics['chunk_count'] > 1

        message = f"✅ {symbol} {direction} ORDER COMPLETED"
        if is_chunked:
            message += f" (MULTIPROCESSING)\n"
        else:
            message += f"\n"

        message += f"📊 EXECUTION SUMMARY:\n"
        message += f"   • Original Size: {abs(quantity):.4f} {symbol.replace('USDT', '')}\n"
        message += f"   • Filled: {metrics['total_filled']:.4f} ({metrics['fill_rate']:.1f}%)\n"

        if is_chunked:
            message += f"   • Chunks Executed: {metrics['chunk_count']} (parallel)\n"

        if execution_time:
            message += f"   • Execution Time: {execution_time:.1f}s\n"

        message += f"\n💰 PRICING DETAILS:\n"
        message += f"   • Avg Execution: ${metrics['avg_execution_price']:,.2f}\n"
        message += f"   • Market Price: ${metrics['current_market_price']:,.2f}\n"

        if metrics['price_range'] and is_chunked:
            message += f"   • Price Range: ${metrics['price_range']['min']:,.2f} - ${metrics['price_range']['max']:,.2f}\n"

        message += f"\n📈 PERFORMANCE METRICS:\n"
        message += f"   • Slippage: {metrics['slippage_bps']:+.1f} bps"
        if metrics['slippage_bps'] > 0:
            message += " (unfavorable)" if side.lower() == 'buy' else " (favorable)"
        else:
            message += " (favorable)" if side.lower() == 'buy' else " (unfavorable)"
        message += f"\n   • Fee Rate: {metrics['fee_rate_bps']:.1f} bps\n"
        message += f"   • Total Fees: ${metrics['total_fees']:.2f}\n"
        message += f"   • Total Cost: ${metrics['total_cost']:,.2f}"

        if is_chunked:
            message += f"\n\n🚀 MULTIPROCESSING BENEFITS:\n"
            message += f"   • Parallel Execution: {metrics['chunk_count']} chunks\n"
            message += f"   • Reduced Market Impact\n"
            message += f"   • Faster Large Order Fills"

        message += f"\n\n🎯 MAKER-ONLY EXECUTION:\n"
        message += f"   • All orders were MAKER orders (postOnly)\n"
        message += f"   • Received maker rebates/lower fees\n"
        message += f"   • No market order fallback used\n"
        message += f"   • Cost savings vs taker orders!"

        send_tg(message)

    except Exception as e:
        print(f"Error sending Telegram notification: {e}")
        # Fallback to simple message
        send_tg(f"✅ {symbol} {side.upper()} order completed: {abs(quantity)}")


def execute_single_chunk_process(args):
    """
    Execute a single chunk in a separate process.

    Args:
        args (tuple): (symbol, side, chunk_quantity, chunk_id, config_dict)

    Returns:
        dict: Chunk execution result
    """
    try:
        symbol, side, chunk_quantity, chunk_id, config_dict = args

        # Reinitialize exchange in the child process
        global EXCHANGE, selected_exchange, keys

        # Restore configuration from passed dict
        selected_exchange = config_dict['selected_exchange']
        keys = config_dict['keys']

        # Initialize exchange based on type
        if selected_exchange == "BYBIT":
            api_key = keys.get('APIKEY', '')
            secret = keys.get('SECRET', '')
            live_account = keys.get('LIVE_ACCOUNT', 'N')
            vip_level = str(keys.get('VIP', '0'))

            # Define rate limits locally since global constants aren't available in child process
            bybit_rate_limits = {
                '0': 10,  # Default
                '1': 20,  # VIP 1
                '2': 40,  # VIP 2
                '3': 60,  # VIP 3
                '4': 60,  # VIP 4
                '5': 60,  # VIP 5
                'S': 60,  # VIP S
                'Default': 10  # Fallback
            }
            rate_limit = bybit_rate_limits.get(vip_level, 10)

            EXCHANGE = ccxt.bybit({
                'apiKey': api_key,
                'secret': secret,
                'enableRateLimit': True,
                'options': {
                    'recvWindow': 10000,
                    'adjustForTimeDifference': True
                },
                'rateLimit': 1000 / rate_limit  # Convert requests/second to ms delay
            })
            if live_account == "N":
                EXCHANGE.enable_demo_trading(True)

        elif selected_exchange == "OKX":
            api_key = keys.get('APIKEY', '')
            secret = keys.get('SECRET', '')
            passphrase = keys.get('PASSPHRASE', '')
            live_account = keys.get('LIVE_ACCOUNT', 'N')

            EXCHANGE = ccxt.okx({
                'apiKey': api_key,
                'secret': secret,
                'password': passphrase,
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'swap',
                    'adjustForTimeDifference': True
                }
            })
            if live_account == "N":
                EXCHANGE.set_sandbox_mode(True)

        else:
            raise Exception(f"Unsupported exchange: {selected_exchange}")

        print(f"   🔄 [CHUNK {chunk_id}] Starting execution: {side} {chunk_quantity}")
        print(
            f"   ⚡ Using OPTIMIZED settings: {LARGE_ORDER_TIMEOUT}s timeout, {LARGE_ORDER_SPREAD * 100:.3f}% spread, {LARGE_ORDER_CHECK_INTERVAL}s intervals")

        # Execute the chunk using the core order function with OPTIMIZED settings
        result = execute_order_with_params(
            symbol=symbol,
            side=side,
            quantity=chunk_quantity,
            timeout_seconds=LARGE_ORDER_TIMEOUT,  # 30s (optimized from 90s)
            spread_percentage=LARGE_ORDER_SPREAD,  # 0.005% (tighter spread)
            check_interval=LARGE_ORDER_CHECK_INTERVAL,  # 2s (faster checking)
            max_attempts=LARGE_ORDER_MAX_ATTEMPTS,  # 10 attempts (faster timeout)
            order_type=f"chunk_{chunk_id}"
        )

        return {
            'chunk_id': chunk_id,
            'success': True,
            'result': result,
            'filled': result.get('filled', 0) if result else 0,
            'error': None
        }

    except Exception as e:
        return {
            'chunk_id': chunk_id,
            'success': False,
            'result': None,
            'filled': 0,
            'error': str(e)
        }


def execute_chunked_order(symbol, side, quantity):
    """
    Execute large orders by splitting them into smaller chunks using multiprocessing.
    Uses dynamic chunk sizing based on USDT value rather than fixed quantity.

    Args:
        symbol (str): Trading symbol
        side (str): 'buy' or 'sell'
        quantity (float): Total order quantity

    Returns:
        Final order result or None
    """
    total_qty = abs(quantity)

    # Get current price for dynamic chunk calculation
    try:
        ticker = EXCHANGE.fetch_ticker(symbol)
        current_price = ticker['last']
        total_usdt_value = total_qty * current_price
    except Exception as e:
        print(f"Error fetching price for chunking: {e}")
        return None

    # Calculate dynamic chunk size based on USDT value
    chunk_size, num_chunks, chunk_usdt_value = calculate_dynamic_chunk_size(symbol, total_qty, current_price)

    print(f"   💰 DYNAMIC CHUNKING ANALYSIS:")
    print(f"      • Total order value: ${total_usdt_value:,.2f} USDT")
    print(f"      • Target chunk value: ${CHUNK_TARGET_USDT_VALUE:,.2f} USDT")
    print(f"      • Calculated chunk size: {chunk_size:.6f} {symbol}")
    print(f"      • Actual chunk value: ${chunk_usdt_value:.2f} USDT")
    print(f"      • Number of chunks: {num_chunks}")

    # Calculate chunks with dynamic sizing
    chunks = []
    remaining_qty = total_qty
    chunk_id = 0

    while remaining_qty > 0 and chunk_id < MAX_CHUNKS_PER_ORDER:
        chunk_id += 1
        current_chunk_size = min(remaining_qty, chunk_size)
        chunk_quantity = current_chunk_size if side.lower() == 'buy' else -current_chunk_size
        chunks.append((symbol, side, chunk_quantity, chunk_id))
        remaining_qty -= current_chunk_size

    print(f"   🔄 Starting OPTIMIZED MULTIPROCESSING chunked execution:")
    print(f"      • Total size: {total_qty}")
    print(f"      • Chunk size: {chunk_size}")
    print(f"      • Total chunks: {len(chunks)}")
    print(f"      • Max workers: {min(MAX_CHUNK_WORKERS, len(chunks))} (INCREASED for speed)")
    print(f"      • Process timeout: {CHUNK_PROCESS_TIMEOUT}s (OPTIMIZED: 1min vs 5min)")
    print(f"      • Total timeout: {CHUNK_MULTIPROCESSING_TIMEOUT}s (OPTIMIZED: 3min vs 10min)")
    print(
        f"      • Chunk settings: {LARGE_ORDER_TIMEOUT}s timeout, {LARGE_ORDER_SPREAD * 100:.3f}% spread, {LARGE_ORDER_CHECK_INTERVAL}s intervals")
    print(f"      • 🎯 ALL ORDERS: 100% MAKER (postOnly=True)")

    # Prepare configuration for child processes
    config_dict = {
        'selected_exchange': selected_exchange,
        'keys': keys,
    }

    # Prepare arguments for multiprocessing
    process_args = [(*chunk, config_dict) for chunk in chunks]

    successful_orders = []
    failed_chunks = []
    start_time = time.time()

    try:
        # Use ProcessPoolExecutor for better timeout handling
        max_workers = min(MAX_CHUNK_WORKERS, len(chunks))

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit all chunk tasks
            future_to_chunk = {
                executor.submit(execute_single_chunk_process, args): args[3]
                for args in process_args
            }

            print(f"   🚀 Submitted {len(future_to_chunk)} chunk processes")

            # Wait for completion with timeout
            try:
                completed_futures = as_completed(
                    future_to_chunk.keys(),
                    timeout=CHUNK_MULTIPROCESSING_TIMEOUT
                )

                for future in completed_futures:
                    chunk_id = future_to_chunk[future]

                    try:
                        # Get result with individual chunk timeout
                        result = future.result(timeout=CHUNK_PROCESS_TIMEOUT)

                        if result['success'] and result['result']:
                            successful_orders.append(result['result'])
                            print(f"   ✅ [CHUNK {chunk_id}] Completed successfully: {result['filled']} filled")
                        else:
                            failed_chunks.append(chunk_id)
                            error_msg = result.get('error', 'Unknown error')
                            print(f"   ❌ [CHUNK {chunk_id}] Failed: {error_msg}")

                    except TimeoutError:
                        failed_chunks.append(chunk_id)
                        print(f"   ⏰ [CHUNK {chunk_id}] Individual timeout ({CHUNK_PROCESS_TIMEOUT}s)")

                    except Exception as e:
                        failed_chunks.append(chunk_id)
                        print(f"   💥 [CHUNK {chunk_id}] Exception: {e}")

            except TimeoutError:
                print(f"   ⏰ MULTIPROCESSING TIMEOUT after {CHUNK_MULTIPROCESSING_TIMEOUT}s")
                print(f"   🛑 Cancelling remaining chunk processes...")

                # Cancel remaining futures
                for future in future_to_chunk.keys():
                    if not future.done():
                        future.cancel()
                        chunk_id = future_to_chunk[future]
                        failed_chunks.append(chunk_id)
                        print(f"   🚫 [CHUNK {chunk_id}] Cancelled due to timeout")

    except Exception as e:
        print(f"   💥 MULTIPROCESSING ERROR: {e}")
        # Fallback to sequential execution
        print(f"   🔄 Falling back to sequential execution...")
        return execute_chunked_order_sequential(symbol, side, quantity)

    execution_time = time.time() - start_time

    # Summary
    print(f"\n   📊 CHUNKED EXECUTION SUMMARY:")
    print(f"      • Total chunks: {len(chunks)}")
    print(f"      • Successful: {len(successful_orders)}")
    print(f"      • Failed: {len(failed_chunks)}")
    print(f"      • Execution time: {execution_time:.1f}s")

    if failed_chunks:
        print(f"      • Failed chunk IDs: {failed_chunks}")

    # Calculate and send metrics
    if successful_orders:
        print(f"   📈 Calculating execution metrics...")
        metrics = calculate_order_metrics(successful_orders, symbol, quantity, side)

        if metrics:
            print(f"   📊 Metrics calculated - sending to Telegram...")
            send_order_completion_telegram(symbol, side, quantity, metrics, execution_time)

        # Return the last successful order as the final result
        return successful_orders[-1] if successful_orders else None
    else:
        print(f"   ❌ No successful chunks - order execution failed")
        send_tg(f"❌ {symbol} {side.upper()} chunked order FAILED: 0/{len(chunks)} chunks successful")
        return None


def execute_chunked_order_sequential(symbol, side, quantity):
    """
    Fallback sequential execution for chunked orders.

    Args:
        symbol (str): Trading symbol
        side (str): 'buy' or 'sell'
        quantity (float): Total order quantity

    Returns:
        Final order result or None
    """
    total_qty = abs(quantity)
    remaining_qty = total_qty
    chunk_count = 0
    successful_orders = []
    start_time = time.time()

    # Get current price and calculate dynamic chunk size
    try:
        ticker = EXCHANGE.fetch_ticker(symbol)
        current_price = ticker['last']
        chunk_size, num_chunks, chunk_usdt_value = calculate_dynamic_chunk_size(symbol, total_qty, current_price)

        print(f"   🔄 Sequential DYNAMIC chunked execution: {total_qty} total")
        print(f"   💰 Dynamic sizing: {num_chunks} chunks of ~${chunk_usdt_value:.2f} USDT each")

    except Exception as e:
        print(f"   ⚠️  Error in dynamic sizing, using fallback: {e}")
        chunk_size = min(total_qty / 10, 1.0)  # Conservative fallback
        print(f"   🔄 Sequential chunked execution: {total_qty} total, {chunk_size} per chunk (fallback)")

    while remaining_qty > 0 and chunk_count < MAX_CHUNKS_PER_ORDER:
        chunk_count += 1
        current_chunk = min(remaining_qty, chunk_size)

        print(f"   📦 Chunk {chunk_count}: {side} {current_chunk} (remaining: {remaining_qty - current_chunk})")

        chunk_result = execute_order_with_params(
            symbol=symbol,
            side=side,
            quantity=current_chunk if side.lower() == 'buy' else -current_chunk,
            timeout_seconds=LARGE_ORDER_TIMEOUT,
            spread_percentage=LARGE_ORDER_SPREAD,
            check_interval=LARGE_ORDER_CHECK_INTERVAL,
            max_attempts=LARGE_ORDER_MAX_ATTEMPTS,
            order_type=f"seq_chunk_{chunk_count}"
        )

        if chunk_result:
            successful_orders.append(chunk_result)
            filled = chunk_result.get('filled', current_chunk)
            remaining_qty -= filled

            if filled < current_chunk:
                print(f"   ⚠️ Chunk {chunk_count} partially filled: {filled}/{current_chunk}")
                remaining_qty = max(0, remaining_qty)
        else:
            print(f"   ❌ Chunk {chunk_count} failed to execute")
            break

        # Small delay between chunks
        if remaining_qty > 0:
            time.sleep(2)

    print(f"   ✅ Sequential execution complete. Total chunks: {chunk_count}")

    # Calculate and send metrics for sequential execution too
    execution_time = time.time() - start_time
    if successful_orders:
        metrics = calculate_order_metrics(successful_orders, symbol, quantity, side)
        if metrics:
            send_order_completion_telegram(symbol, side, quantity, metrics, execution_time)

    return successful_orders[-1] if successful_orders else None


def execute_order_with_params(symbol, side, quantity, timeout_seconds, spread_percentage,
                              check_interval, max_attempts, order_type="standard"):
    """
    Core order execution function with configurable parameters.

    Args:
        symbol (str): Trading symbol
        side (str): 'buy' or 'sell'
        quantity (float): Order quantity
        timeout_seconds (int): Timeout per attempt
        spread_percentage (float): Price improvement percentage
        check_interval (int): Status check interval
        max_attempts (int): Maximum attempts
        order_type (str): Order type for logging

    Returns:
        Order result or None
    """
    global EXCHANGE
    total_qty = abs(quantity)
    remaining_qty = total_qty
    attempt = 0
    final_order = None

    # Calculate the appropriate delay between API calls based on VIP level
    if selected_exchange == "BYBIT":
        vip_level = str(keys.get('VIP', '0'))
        api_call_delay = 1.0 / BYBIT_RATE_LIMITS.get(vip_level, 10)
    else:
        api_call_delay = 0.1  # Default delay for other exchanges

    # Ensure we have valid credentials
    ensure_exchange_credentials()

    # MAKER-ONLY MODE: Use infinite retry loop if enabled
    while remaining_qty > 0:
        # Check if we should respect max_attempts or retry infinitely
        if not INFINITE_RETRY_MODE and attempt >= max_attempts:
            print(f"[{order_type.upper()}] ❌ Maximum attempts ({max_attempts}) reached with {remaining_qty} unfilled")
            print(f"[{order_type.upper()}] 🎯 MAKER-ONLY MODE: No market order fallback available")
            return final_order

        attempt += 1
        max_display = "∞" if INFINITE_RETRY_MODE else str(max_attempts)
        print(f"[{order_type.upper()}] Attempt {attempt}/{max_display}: Placing MAKER order for {remaining_qty}")

        try:
            # Step 1: Get the latest order book and determine the price
            order_book = EXCHANGE.fetch_order_book(symbol)
            time.sleep(api_call_delay)

            if side.lower() == 'buy':
                best_ask = order_book['asks'][0][0]
                limit_price = best_ask * (1 - spread_percentage)
                print(f"[{order_type.upper()}] Latest ask: {best_ask}, limit_price: {limit_price}")
            else:
                best_bid = order_book['bids'][0][0]
                limit_price = best_bid * (1 + spread_percentage)
                print(f"[{order_type.upper()}] Latest bid: {best_bid}, limit_price: {limit_price}")

            # Step 2: Place the limit order with MANDATORY postOnly for maker-only mode
            order_params = {'postOnly': True}  # ALWAYS force maker orders

            final_order = EXCHANGE.create_order(
                symbol=symbol,
                type='limit',
                side=side,
                amount=remaining_qty,
                price=limit_price,
                params=order_params
            )
            time.sleep(api_call_delay)

            order_id = final_order.get('id')
            if not order_id:
                print(f"[{order_type.upper()}] Order ID not found. Retrying...")
                time.sleep(1)
                continue

            print(f"[{order_type.upper()}] 🎯 MAKER order created! ID: {order_id}, tracking for {timeout_seconds}s...")

            # Step 3: Track order status
            filled = 0
            seconds = 0

            while seconds < timeout_seconds and filled < remaining_qty:
                time.sleep(check_interval)
                seconds += check_interval

                try:
                    if selected_exchange == "BINANCE":
                        updated_order = EXCHANGE.fetch_order(order_id, symbol)
                    elif selected_exchange == "BYBIT":
                        updated_order = EXCHANGE.fetch_open_order(order_id, symbol)
                    elif selected_exchange == "OKX":
                        updated_order = EXCHANGE.fetch_order(order_id, symbol)
                    time.sleep(api_call_delay)

                    filled = updated_order.get('filled', 0)
                    print(
                        f"[{order_type.upper()}] Attempt {attempt}, Order {order_id}: Filled {filled}/{remaining_qty} after {seconds}s")
                except Exception as e:
                    print(f"[{order_type.upper()}] Error fetching order status: {e}")
                    ensure_exchange_credentials()
                    continue

            # Step 4: Handle completion or cancellation
            if filled >= remaining_qty:
                print(f"[{order_type.upper()}] ✅ Order filled completely on attempt {attempt}!")
                return final_order
            else:
                # Cancel unfilled order
                try:
                    EXCHANGE.cancel_order(order_id, symbol)
                    time.sleep(api_call_delay)
                    print(f"[{order_type.upper()}] 🚫 Cancelled unfilled order {order_id} after {timeout_seconds}s")
                except Exception as e:
                    print(f"[{order_type.upper()}] Failed to cancel order {order_id}: {e}")
                    ensure_exchange_credentials()

                # Update remaining quantity
                remaining_qty -= filled
                print(f"[{order_type.upper()}] Remaining to fill: {remaining_qty}")

                # Exit if remaining quantity is too small
                if remaining_qty < MIN_POS:
                    print(
                        f"[{order_type.upper()}] ✅ Remaining quantity {remaining_qty} below MIN_POS {MIN_POS}, order complete.")
                    return final_order

                # Brief delay before next attempt
                time.sleep(2)

        except Exception as e:
            error_msg = str(e)
            print(f"[{order_type.upper()}] Error on attempt {attempt}: {e}")

            # Use centralized error categorization
            current_max = max_attempts if not INFINITE_RETRY_MODE else 999
            is_fatal_error, error_category, user_message = categorize_trading_error(error_msg, attempt, current_max)

            if is_fatal_error and not INFINITE_RETRY_MODE:
                print(f"[{order_type.upper()}] ❌ FATAL ERROR ({error_category})")
                print(f"[{order_type.upper()}] ❌ {user_message}")
                raise Exception(f"{error_category}: {error_msg}")
            elif is_fatal_error and INFINITE_RETRY_MODE:
                print(f"[{order_type.upper()}] ⚠️ FATAL ERROR in infinite retry mode ({error_category})")
                print(f"[{order_type.upper()}] ⚠️ {user_message}")
                print(f"[{order_type.upper()}] 🔄 Will retry after delay due to INFINITE_RETRY_MODE...")
                time.sleep(10)  # Longer delay for fatal errors
                continue

            print(f"[{order_type.upper()}] ⚠️ Retryable error ({error_category}): {user_message}")
            ensure_exchange_credentials()
            time.sleep(2)

    print(f"[{order_type.upper()}] ✅ All quantity filled successfully!")
    return final_order


def aggressive_limit_order(symbol, side, quantity):
    """
    Smart order execution that automatically chooses between small and large order strategies.

    Args:
        symbol (str): Trading symbol
        side (str): 'buy' or 'sell'
        quantity (float): Order quantity

    Returns:
        Order result or None
    """
    # Step 1: Classify order size and choose execution strategy
    is_large_order, usd_value, classification_reason = classify_order_size(symbol, quantity)

    print(f"\n📊 ORDER CLASSIFICATION:")
    print(f"   Order Size: {abs(quantity)} {symbol}")
    print(f"   USD Value: ${usd_value:,.2f}")
    print(f"   Classification: {'🐋 LARGE ORDER' if is_large_order else '🚀 SMALL ORDER'}")
    print(f"   Reason: {classification_reason}")
    print(f"   Thresholds: Quantity >= {LARGE_ORDER_THRESHOLD} OR USD >= ${LARGE_ORDER_USD_THRESHOLD:,.2f}")

    # Step 2: Execute using appropriate strategy
    try:
        if is_large_order:
            return execute_large_order(symbol, side, quantity)
        else:
            return execute_small_order(symbol, side, quantity)
    except Exception as e:
        print(f"❌ Order execution failed: {e}")
        raise


def get_symbol_constraints(symbol):
    """
    Get symbol-specific trading constraints from the exchange.

    Args:
        symbol (str): Trading symbol

    Returns:
        dict: Symbol constraints including min_amount, amount_precision, etc.
    """
    try:
        market = EXCHANGE.market(symbol)
        constraints = {
            'min_amount': market['limits']['amount']['min'],
            'amount_precision': market['precision']['amount'],
            'price_precision': market['precision']['price'],
            'min_notional': market['limits']['cost']['min'] if market['limits']['cost']['min'] else 0,
            'symbol': symbol
        }
        return constraints
    except Exception as e:
        print(f"Error getting symbol constraints for {symbol}: {e}")
        # Return safe defaults
        return {
            'min_amount': 0.01,  # Conservative default
            'amount_precision': 0.01,
            'price_precision': 0.01,
            'min_notional': 1.0,
            'symbol': symbol
        }


def calculate_dynamic_chunk_size(symbol, total_quantity, current_price=None):
    """
    Calculate dynamic chunk size based on USDT value with symbol-specific constraints.

    Args:
        symbol (str): Trading symbol
        total_quantity (float): Total order quantity
        current_price (float): Current price (fetched if not provided)

    Returns:
        tuple: (chunk_size_in_quantity, num_chunks, chunk_usdt_value)
    """
    try:
        # Get current price if not provided
        if current_price is None:
            ticker = EXCHANGE.fetch_ticker(symbol)
            current_price = ticker['last']

        # Get symbol-specific constraints
        constraints = get_symbol_constraints(symbol)
        min_amount = constraints['min_amount']
        amount_precision = constraints['amount_precision']

        total_usdt_value = abs(total_quantity) * current_price
        min_notional = max(get_minimum_notional(symbol), constraints['min_notional'])

        # Ensure minimum chunk value is above notional requirements
        effective_min_chunk_usdt = max(MIN_CHUNK_USDT_VALUE, min_notional * 2)  # 2x safety margin

        # Calculate target chunk size in quantity
        target_chunk_quantity = CHUNK_TARGET_USDT_VALUE / current_price

        # Ensure target chunk meets minimum amount requirement
        target_chunk_quantity = max(target_chunk_quantity, min_amount * 2)  # 2x minimum for safety

        # Round to proper precision
        if amount_precision > 0:
            target_chunk_quantity = round(target_chunk_quantity / amount_precision) * amount_precision

        # Calculate number of chunks needed
        estimated_chunks = max(1, int(abs(total_quantity) / target_chunk_quantity))

        # For very large orders, use larger chunks to avoid excessive fragmentation
        if total_usdt_value > 1000000:  # Orders > $1M USD
            # Use larger chunks for very large orders
            target_chunk_usdt = min(MAX_CHUNK_USDT_VALUE, total_usdt_value / 20)  # Max 20 chunks for huge orders
            target_chunk_quantity = target_chunk_usdt / current_price
            estimated_chunks = max(1, int(abs(total_quantity) / target_chunk_quantity))
            print(f"💰 Large order optimization: Using ${target_chunk_usdt:.0f} USDT chunks for efficiency")

        # Limit maximum number of chunks to prevent infinite loops
        if estimated_chunks > MAX_CHUNKS_PER_ORDER:
            estimated_chunks = MAX_CHUNKS_PER_ORDER
            print(f"⚠️  Limiting chunks to maximum {MAX_CHUNKS_PER_ORDER} to prevent excessive fragmentation")

        # Calculate actual chunk size
        actual_chunk_quantity = abs(total_quantity) / estimated_chunks

        # Ensure chunk meets minimum amount requirement
        if actual_chunk_quantity < min_amount:
            print(f"⚠️  Chunk size {actual_chunk_quantity:.6f} below minimum {min_amount} for {symbol}")
            actual_chunk_quantity = min_amount
            estimated_chunks = max(1, int(abs(total_quantity) / actual_chunk_quantity))
            print(f"📏 Adjusted to minimum chunk size: {actual_chunk_quantity:.6f} {symbol}")

        # Round to proper precision
        if amount_precision > 0:
            actual_chunk_quantity = round(actual_chunk_quantity / amount_precision) * amount_precision

        actual_chunk_usdt_value = actual_chunk_quantity * current_price

        # Validate chunk size meets minimum requirements
        if actual_chunk_usdt_value < effective_min_chunk_usdt:
            # Recalculate with minimum chunk size
            actual_chunk_quantity = effective_min_chunk_usdt / current_price
            actual_chunk_quantity = max(actual_chunk_quantity, min_amount)
            if amount_precision > 0:
                actual_chunk_quantity = round(actual_chunk_quantity / amount_precision) * amount_precision
            estimated_chunks = max(1, int(abs(total_quantity) / actual_chunk_quantity))
            actual_chunk_usdt_value = actual_chunk_quantity * current_price

            print(f"📏 Adjusted chunk size to meet minimum notional: ${actual_chunk_usdt_value:.2f} USDT")

        # Final validation to prevent infinite loops
        if estimated_chunks > MAX_CHUNKS_PER_ORDER:
            estimated_chunks = MAX_CHUNKS_PER_ORDER
            actual_chunk_quantity = abs(total_quantity) / estimated_chunks
            actual_chunk_quantity = max(actual_chunk_quantity, min_amount)
            if amount_precision > 0:
                actual_chunk_quantity = round(actual_chunk_quantity / amount_precision) * amount_precision
            actual_chunk_usdt_value = actual_chunk_quantity * current_price
            print(f"⚠️  Final adjustment: {estimated_chunks} chunks of ${actual_chunk_usdt_value:.2f} USDT each")

        print(f"🔍 Symbol constraints for {symbol}: min_amount={min_amount}, precision={amount_precision}")
        print(f"📊 Final chunk: {actual_chunk_quantity:.6f} {symbol} (${actual_chunk_usdt_value:.2f} USDT)")

        return actual_chunk_quantity, estimated_chunks, actual_chunk_usdt_value

    except Exception as e:
        print(f"Error calculating dynamic chunk size: {e}")
        # Fallback to conservative chunking with symbol constraints
        try:
            constraints = get_symbol_constraints(symbol)
            min_amount = constraints['min_amount']
            fallback_chunk_quantity = max(min_amount * 5, abs(total_quantity) / 10)  # 5x minimum or 1/10 total
            fallback_usdt_value = fallback_chunk_quantity * (current_price or 50000)
            return fallback_chunk_quantity, 10, fallback_usdt_value
        except:
            # Ultimate fallback
            fallback_chunk_quantity = min(abs(total_quantity) / 10, 1.0)
            fallback_usdt_value = fallback_chunk_quantity * (current_price or 50000)
            return fallback_chunk_quantity, 10, fallback_usdt_value


def get_minimum_notional(symbol):
    """
    Get the minimum notional value required for the given symbol.
    For Binance futures, this is typically 20 USDT.

    Args:
        symbol (str): The trading symbol

    Returns:
        float: The minimum notional value required
    """
    try:
        if selected_exchange == "BINANCE":
            # Minimum notional for Binance futures is 20 USDT
            return 20.0
        elif selected_exchange == "BYBIT":
            # Bybit typically has 1 USDT minimum for most pairs
            return 1.0
        elif selected_exchange == "OKX":
            # OKX typically has 1 USDT minimum
            return 1.0
        else:
            return 1.0
    except Exception as e:
        print(f"Error getting minimum notional: {e}")
        # Default to 20.0 as a safe value for Binance
        return 20.0


def check_minimum_notional(symbol, quantity):
    """
    Check if an order meets the minimum notional value requirement.

    Args:
        symbol (str): The trading symbol
        quantity (float): The quantity to trade

    Returns:
        bool: True if the order meets minimum notional requirements, False otherwise
    """
    try:
        min_notional = get_minimum_notional(symbol)

        if min_notional <= 0:
            return True  # No minimum notional requirement

        # Get current price for the symbol
        ticker = EXCHANGE.fetch_ticker(symbol)
        current_price = ticker['last']

        # Calculate notional value (quantity * price)
        notional_value = abs(quantity) * current_price

        # Check if notional value meets minimum requirement
        meets_requirement = notional_value >= min_notional

        if not meets_requirement:
            print(f"Order doesn't meet minimum notional requirement: {notional_value:.2f} < {min_notional} USD")

        return meets_requirement

    except Exception as e:
        print(f"Error checking minimum notional: {e}")
        # Default to True to avoid blocking trades
        return True


def execute_trade(signal, execute_orders=False):
    """Execute trades based on the calculated signal"""
    # symbol_found is used to determine if the symbol exists in max_positions.csv
    # AND if rebalance process has fully completed (file is stable)
    _, _, _, symbol_found = check_max_positions_updated(force_reload=True)

    # Get current position
    net_pos = current_pos()

    # FIXED: Calculate target position using the full MAX_POS value
    # The signal is typically between -1 and 1, so this will use the proper scaling
    target_pos = MAX_POS * signal

    bet_size = round(target_pos - net_pos, PRECISION)

    # Apply minimum position size check
    if abs(bet_size) < MIN_POS:
        print(f"Bet size {bet_size} below minimum {MIN_POS}, setting to 0")
        bet_size = 0

    print("\n" + "=" * 60)
    print("🎯 TRADE EXECUTION CALCULATION")
    print("=" * 60)
    print(f"📊 Position Details:")
    print(f"  • Current Position: {net_pos}")
    print(f"  • Target Position: {target_pos} (MAX_POS: {MAX_POS} × Signal: {signal:.6f})")
    print(f"  • Required Trade Size: {target_pos} - {net_pos} = {bet_size}")
    print(f"  • Precision: {PRECISION} decimal places")
    print(f"  • Minimum Position Size: {MIN_POS}")

    if abs(bet_size) < MIN_POS:
        print(f"  • ⚠️  Trade size {bet_size} is below minimum {MIN_POS}")

    trade_direction = "BUY" if bet_size > 0 else "SELL" if bet_size < 0 else "NO TRADE"
    print(f"  • Trade Direction: {trade_direction}")
    print("=" * 60)

    if bet_size == 0:
        print("No trade needed - position already at target")

        # Still record strategy signals even when no trade occurs
        if latest_strategy_positions:
            # Get current market price for recording purposes
            try:
                ticker = EXCHANGE.fetch_ticker(SYMBOL)
                current_price = ticker['last']
                current_timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                record_strategy_signals(
                    timestamp=current_timestamp,
                    execute_price=current_price,
                    weighted_total_pos=signal,
                    individual_strategy_positions=latest_strategy_positions,
                    symbol=SYMBOL
                )
            except Exception as e:
                print(f"Warning: Could not record strategy signals for no-trade cycle: {e}")

        log_trade_execution(SYMBOL, 'NO_TRADE', 'SKIPPED', bet_size=0, current_pos=net_pos,
                            target_pos=target_pos, error_reason='Position already at target')
        return

    # Check minimum notional value requirement (especially for Binance)
    if not check_minimum_notional(SYMBOL, bet_size):
        min_notional = get_minimum_notional(SYMBOL)
        try:
            ticker = EXCHANGE.fetch_ticker(SYMBOL)
            current_price = ticker['last']
            notional_value = abs(bet_size) * current_price
            min_required_size = min_notional / current_price

            print(f"❌ TRADE REJECTED - Below minimum notional value:")
            print(f"  • Current trade size: {abs(bet_size)} {SYMBOL}")
            print(f"  • Current price: ${current_price:,.2f}")
            print(f"  • Trade notional value: ${notional_value:.2f}")
            print(f"  • Exchange minimum notional: ${min_notional:.2f}")
            print(f"  • Minimum required size: {min_required_size:.6f} {SYMBOL}")
            print(f"  • Shortfall: ${min_notional - notional_value:.2f}")

            # Send detailed notification
            send_tg(
                f"❌ {SYMBOL} trade rejected: Size {abs(bet_size)} × ${current_price:,.2f} = ${notional_value:.2f} < ${min_notional:.2f} minimum. Need ≥{min_required_size:.6f} {SYMBOL}")

        except Exception as e:
            print(f"❌ TRADE REJECTED - Below minimum notional value requirement")
            print(f"Error getting detailed info: {e}")
            send_tg(f"❌ {SYMBOL} trade rejected: Order size ${abs(bet_size)} below ${min_notional} minimum notional")

        # Still record strategy signals even when trade is rejected
        if latest_strategy_positions:
            try:
                ticker = EXCHANGE.fetch_ticker(SYMBOL)
                current_price = ticker['last']
                current_timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                record_strategy_signals(
                    timestamp=current_timestamp,
                    execute_price=current_price,
                    weighted_total_pos=signal,
                    individual_strategy_positions=latest_strategy_positions,
                    symbol=SYMBOL
                )
            except Exception as e:
                print(f"Warning: Could not record strategy signals for rejected trade: {e}")

        log_trade_execution(SYMBOL, 'BUY' if bet_size > 0 else 'SELL', 'FAILED',
                            bet_size=bet_size, current_pos=net_pos, target_pos=target_pos,
                            error_reason=f'Below minimum notional value requirement (${min_notional})')
        return

    # Execute order only if execute_orders is True AND symbol exists in max_positions.csv
    # AND rebalance process has fully completed (file is stable)
    if not execute_orders:
        print("Trading execution is disabled - execute_orders flag is False")
        log_trade_execution(SYMBOL, 'BUY' if bet_size > 0 else 'SELL', 'SKIPPED',
                            bet_size=bet_size, current_pos=net_pos, target_pos=target_pos,
                            error_reason='Trading execution disabled (execute_orders=False)')
        return

    if not symbol_found:
        # At this point, symbol_found could be False either because:
        # 1. Symbol is not in max_positions.csv, or
        # 2. Rebalance process is still ongoing (max_positions.csv is still being updated)

        # Check if the symbol exists but rebalance is still in progress
        data_dir = ensure_data_directory()
        max_positions_file = os.path.join(data_dir, 'max_positions.csv')

        error_reason = "Unknown symbol/rebalance issue"
        if os.path.exists(max_positions_file):
            try:
                df = pd.read_csv(max_positions_file)
                symbol_exists = SYMBOL in df['symbol'].values

                if symbol_exists:
                    print("Trading execution is paused - waiting for Rebalance.py to fully complete")
                    print("This ensures correct max position values are used for trading")
                    error_reason = "Waiting for Rebalance.py to complete"
                    send_tg(
                        f"⏳ {SYMBOL} waiting for Rebalance.py to complete. Trade execution paused to ensure correct position sizing.")
                else:
                    print("Trading execution is paused - symbol not found in max_positions.csv")
                    error_reason = "Symbol not found in max_positions.csv"
                    send_tg(f"⏳ {SYMBOL} waiting for symbol in max_positions.csv. Trade execution paused.")
            except Exception as e:
                print(f"Error checking max_positions.csv: {e}")
                print("Trading execution is paused - waiting for valid max_positions.csv")
                error_reason = f"Error reading max_positions.csv: {str(e)}"
                send_tg(f"⏳ {SYMBOL} waiting for valid max_positions.csv. Trade execution paused.")
        else:
            print("Trading execution is paused - max_positions.csv not found")
            error_reason = "max_positions.csv file not found"
            send_tg(f"⏳ {SYMBOL} waiting for max_positions.csv file. Trade execution paused.")

        log_trade_execution(SYMBOL, 'BUY' if bet_size > 0 else 'SELL', 'SKIPPED',
                            bet_size=bet_size, current_pos=net_pos, target_pos=target_pos,
                            error_reason=error_reason)
        return

    # If we get here, we're executing the trade
    print(f"Executing trade for {SYMBOL}: {bet_size}")

    # Proceed with order execution
    if selected_exchange == "BYBIT" or selected_exchange == "BINANCE":
        mid_price = None

    action = 'BUY' if bet_size > 0 else 'SELL'
    order_type = "limit"  # MAKER-ONLY MODE: Force all orders to be limit orders

    print(f"🎯 MAKER-ONLY MODE: Executing {action} order for {abs(bet_size)} {SYMBOL}")
    print(f"💰 Expected maker rebate instead of taker fees!")

    for attempt in range(3):
        try:
            order_result = None
            # MAKER-ONLY MODE: All orders are limit orders with postOnly
            if bet_size > 0:
                order_result = aggressive_limit_order(SYMBOL, 'buy', bet_size)
                # Only print success if we actually got a valid order result
                if order_result and order_result.get('id'):
                    print(f'✅ Maker Limit Order Buy {bet_size} {SYMBOL} successfully!')
                else:
                    raise Exception("Order returned but no valid order ID found")
            elif bet_size < 0:
                order_result = aggressive_limit_order(SYMBOL, 'sell', abs(bet_size))
                # Only print success if we actually got a valid order result
                if order_result and order_result.get('id'):
                    print(f'✅ Maker Limit Sell {abs(bet_size)} {SYMBOL} successfully!')
                else:
                    raise Exception("Order returned but no valid order ID found")

            # Extract order details for logging
            order_id = order_result.get('id') if order_result else None
            price = order_result.get('price') or order_result.get('average') if order_result else None

            # Verify we have a valid order before logging success
            if not order_result or not order_id:
                raise Exception("Order execution failed - no valid order returned")

            # Get execution timestamp from order result
            execution_timestamp = get_order_timestamp(order_result)

            # Record individual strategy signals for this trade cycle
            if latest_strategy_positions and price is not None:
                record_strategy_signals(
                    timestamp=execution_timestamp,
                    execute_price=price,
                    weighted_total_pos=signal,
                    individual_strategy_positions=latest_strategy_positions,
                    symbol=SYMBOL
                )

            # Log successful trade
            log_trade_execution(SYMBOL, action, 'SUCCESS', bet_size=bet_size,
                                current_pos=net_pos, target_pos=target_pos, price=price,
                                order_id=order_id, order_type=order_type)

            # Send success notification
            direction = "BUY" if bet_size > 0 else "SELL"
            send_tg(f"✅ {SYMBOL} {direction} order executed successfully! Size: {abs(bet_size)}, Order ID: {order_id}")
            break

        except Exception as e:
            error_msg = str(e)

            # Use centralized error categorization
            is_fatal_error, error_category, user_message = categorize_trading_error(error_msg, attempt, 3)

            if is_fatal_error:
                print(f"❌ TRADE FAILED: {error_category}")
                print(f"❌ {user_message}")
                print(f"❌ Error: {error_msg}")

                # Log the failure immediately for fatal errors (don't retry)
                log_trade_execution(SYMBOL, action, 'FAILED', bet_size=bet_size,
                                    current_pos=net_pos, target_pos=target_pos,
                                    error_reason=f"{error_category}: {error_msg}",
                                    order_type=order_type)

                # Send failure notification with appropriate message
                if error_category == "Insufficient Balance":
                    send_tg(f"❌ {SYMBOL} trade FAILED: {error_category}. Required: {abs(bet_size)} {SYMBOL}")
                else:
                    send_tg(f"❌ {SYMBOL} trade FAILED: {error_category}. {user_message}")
                return  # Exit immediately, don't retry
            else:
                print(f"⚠️  Trade attempt {attempt + 1}/3 failed ({error_category}): {user_message}")

            # Log failed attempt (only log on final failure for retryable errors)
            if attempt == 2:  # Last attempt (0-indexed)
                log_trade_execution(SYMBOL, action, 'FAILED', bet_size=bet_size,
                                    current_pos=net_pos, target_pos=target_pos,
                                    error_reason=f"Order failed after 3 attempts: {error_msg}",
                                    order_type=order_type)

                # Send failure notification for final failure
                send_tg(f"❌ {SYMBOL} trade FAILED after 3 attempts: {error_category} - {user_message}")

            # Try to reinitialize credentials in case of auth error
            ensure_exchange_credentials()
            time.sleep(2 ** attempt)  # Wait longer with each retry


def check_weight_ratios_updated():
    """
    This function was previously used to check if weight_ratio has been updated by Rebalance.py.
    It now always returns True for compatibility with existing code while removing the dependency
    on weight_ratio updates from Rebalance.py.

    Returns:
        bool: Always returns True
    """
    # Simply return True to avoid trade execution being blocked by weight_ratio checks
    # The actual weighting logic has been moved to calculate_position
    return True


def send_tg(message):
    """
    Send message via Telegram bot.
    Args:
        message (str): Message to send.
    """
    # Use flat key structure
    try:
        bot_token = keys.get('TG_BOT_TOKEN', '')
        chat_id = keys.get('TG_CHATID', '')

        if not bot_token or not chat_id:
            print("Telegram notification disabled: Missing bot_token or chat_id")
            return

        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

        data = {
            "chat_id": chat_id,
            "text": message  # Send the message without any special formatting
        }

        # Implement retry logic for Telegram API rate limiting
        max_retries = 5
        retry_count = 0
        success = False

        while not success and retry_count < max_retries:
            response = requests.post(url, data=data)

            if response.status_code == 200:
                print("Telegram notification sent successfully")
                success = True
            elif response.status_code == 429:
                # Rate limited - parse the retry_after value
                try:
                    retry_after = response.json().get('parameters', {}).get('retry_after', 3)
                except Exception:
                    retry_after = 1  # Default to 1 seconds if we can't parse the response

                retry_count += 1
                print(
                    f"Telegram rate limited. Retrying after {retry_after} seconds (attempt {retry_count}/{max_retries})")
                time.sleep(retry_after)
            else:
                print(f"Failed to send Telegram message: {response.text}")
                # For other errors, wait a bit longer before retrying
                retry_count += 1
                if retry_count < max_retries:
                    print(f"Retrying in 3 seconds (attempt {retry_count}/{max_retries})")
                    time.sleep(3)
                else:
                    break

        if not success:
            print(f"Failed to send Telegram message after {max_retries} attempts")

    except Exception as e:
        print(f"Error sending Telegram notification: {e}")
        # Don't raise exception to avoid interrupting the main program flow


def ensure_data_directory():
    """Create 'data' directory if it doesn't exist"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(current_dir, 'data')
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
        print(f"Created data directory: {data_dir}")
    return data_dir


def ensure_cex_data_directory():
    """Create 'cex_data' directory if it doesn't exist"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    cex_data_dir = os.path.join(current_dir, 'cex_data')
    if not os.path.exists(cex_data_dir):
        os.makedirs(cex_data_dir)
        print(f"Created cex_data directory: {cex_data_dir}")
    return cex_data_dir


def migrate_cex_data_files():
    """
    Migrate existing CEX data files from 'data' directory to 'cex_data' directory.
    This is a one-time migration to support the directory structure change.
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(current_dir, 'data')
    cex_data_dir = ensure_cex_data_directory()

    # Only attempt migration if data directory exists
    if not os.path.exists(data_dir):
        print("No data directory found. No migration needed.")
        return

    # Look for cex_data files in the data directory
    migrated_count = 0
    try:
        for filename in os.listdir(data_dir):
            if filename.startswith('cex_data_') and filename.endswith('.csv'):
                source_path = os.path.join(data_dir, filename)
                target_path = os.path.join(cex_data_dir, filename)

                # Check if file already exists in target directory
                if os.path.exists(target_path):
                    # Compare modification times to see which is newer
                    source_mtime = os.path.getmtime(source_path)
                    target_mtime = os.path.getmtime(target_path)

                    if source_mtime > target_mtime:
                        # Source is newer, copy it
                        import shutil
                        shutil.copy2(source_path, target_path)
                        print(f"Updated {filename} in cex_data directory (newer version found)")
                        migrated_count += 1
                else:
                    # File doesn't exist in target, copy it
                    import shutil
                    shutil.copy2(source_path, target_path)
                    print(f"Migrated {filename} to cex_data directory")
                    migrated_count += 1

        if migrated_count > 0:
            print(f"Successfully migrated {migrated_count} CEX data files to cex_data directory")
        else:
            print("No CEX data files found to migrate")
    except Exception as e:
        print(f"Error during CEX data migration: {e}")


def ensure_max_positions_file():
    """
    Create max_positions.csv file with default values if it doesn't exist.
    Returns the path to the file.
    """
    data_dir = ensure_data_directory()
    max_positions_file = os.path.join(data_dir, 'max_positions.csv')

    if not os.path.exists(max_positions_file) or os.path.getsize(max_positions_file) == 0:
        print(f"Max positions file not found or empty. Creating default file at {max_positions_file}")

        # Create a default max_positions.csv with conservative values
        default_df = pd.DataFrame({
            'symbol': [SYMBOL],
            'strategy_count': [len(config['STRATEGIES'])],
            'weight': [1.0],
            'fund_allocation': [100000.0],  # Conservative default allocation
            'max_pos': [1.0]  # Default to small position size
        })

        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(max_positions_file), exist_ok=True)

            # Save the DataFrame to CSV
            default_df.to_csv(max_positions_file, index=False)
            print(f"Created default max_positions.csv with MAX_POS: {default_df['max_pos'][0]}")
        except Exception as e:
            print(f"Error creating max_positions.csv: {e}")
            # Create a fallback in-memory option
            return max_positions_file

    return max_positions_file


def ensure_strategy_performance_file():
    """
    Return the path to strategy_performance.csv file.
    DOES NOT create the file if it doesn't exist - the file must be created by Rebalance.py
    """
    data_dir = ensure_data_directory()
    performance_file = os.path.join(data_dir, 'strategy_performance.csv')

    if not os.path.exists(performance_file):
        print(f"Warning: Strategy performance file not found at {performance_file}")
        print(f"The strategy_performance.csv file must be created by Rebalance.py first")

    return performance_file


def run_initial_execution():
    """Run initial data fetch and trading operations at startup"""
    global process_data_results, fetch_data_mp
    apply_strategy.initial_load = True
    print('Running initial strategy execution at startup...')
    start_time = time.time()

    # Ensure exchange credentials are valid before starting
    if selected_exchange == "OKX":
        # For OKX, we need to be more careful about initialization
        retries = 3
        for attempt in range(retries):
            try:
                if ensure_exchange_credentials():
                    print(f"OKX credentials verified on attempt {attempt + 1}")
                    break
                else:
                    print(f"OKX credentials verification failed on attempt {attempt + 1}")
                    if attempt < retries - 1:
                        backoff_time = exponential_backoff(attempt)
                        print(f"Retrying in {backoff_time:.2f} seconds...")
                        time.sleep(backoff_time)
            except Exception as e:
                print(f"Error verifying OKX credentials: {e}")
                if attempt < retries - 1:
                    backoff_time = exponential_backoff(attempt)
                    print(f"Retrying in {backoff_time:.2f} seconds...")
                    time.sleep(backoff_time)

    try:
        # Use a smaller pool for OKX to avoid overwhelming rate limits
        processes = min(len(config['STRATEGIES']),
                        1 if selected_exchange == "OKX" else mp.cpu_count())

        # Add better error handling for multiprocessing
        try:
            # Prepare strategies with global variables for multiprocessing
            strategies_with_globals = [prepare_strategy_with_globals(strategy) for strategy in config['STRATEGIES']]

            with mp.Pool(processes=processes) as pool:
                raw_results = pool.map(fetch_data_mp, strategies_with_globals)
        except (BrokenPipeError, OSError) as mp_error:
            print(f"Multiprocessing error: {mp_error}")
            print("Falling back to sequential processing...")
            # Fallback to sequential processing
            raw_results = []
            for strategy in config['STRATEGIES']:
                try:
                    strategy_with_globals = prepare_strategy_with_globals(strategy)
                    result = fetch_data_mp(strategy_with_globals)
                    raw_results.append(result)
                except Exception as e:
                    print(f"Error processing strategy {strategy['name']}: {e}")
                    # Create empty result for failed strategy
                    raw_results.append({
                        "strategy_name": strategy['name'],
                        "df": pd.DataFrame(columns=['t', 'value', 'price'])
                    })

        # Process and convert results to ensure they are in the correct format
        # If we got an integer back, create a proper dict with empty DataFrame
        processed_results = []
        for i, result in enumerate(raw_results):
            if isinstance(result, int):
                print(f"Result {i} is an integer: {result}, converting to proper format")
                # Create a dictionary result with the strategy name and empty DataFrame
                strategy_name = config['STRATEGIES'][i]['name'] if i < len(config['STRATEGIES']) else f"unknown_{i}"
                processed_result = {
                    "strategy_name": strategy_name,
                    "df": pd.DataFrame(columns=['t', 'value', 'price'])
                }
                processed_results.append(processed_result)
            else:
                processed_results.append(result)

        # Remove debug print statements
        # Process the preprocessed results
        process_data_results(processed_results, is_initial=True)

        # Get the signal and check if symbol is in max_positions.csv
        signal = calculate_position()
        _, _, _, symbol_found = check_max_positions_updated(force_reload=True)

        # Run calculations and execute trades if symbol is found
        execute_trade(signal, execute_orders=symbol_found)

        apply_strategy.initial_load = False
        elapsed_time = time.time() - start_time
        print(f"Initial execution completed in {elapsed_time:.2f} seconds")
        execution_msg = f"🚀 {SYMBOL} initial run completed in {elapsed_time:.2f} seconds"
        time.sleep(1)
        gc.collect()  # Explicitly free unused memory after sleep every iteration
        send_tg(execution_msg)
    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = f"Initial execution error: {str(e)}"
        print(error_msg)
        print(f"Initial execution failed after {elapsed_time:.2f} seconds")

        apply_strategy.initial_load = False

        # For rate limit errors, use our backoff function but don't send alerts
        if "Too Many Requests" in str(e) or "rate limit" in str(e).lower():
            backoff_time = exponential_backoff(2)  # Use a larger backoff for initial run failures
            print(f"Rate limit hit during initial load, backing off for {backoff_time:.2f} seconds...")
            time.sleep(backoff_time)
        else:
            # Send notification for non-rate-limit errors
            send_tg(f"⚠️ {SYMBOL} initial run error: {str(e)}")

        gc.collect()  # Free memory even on error


def process_data_results(results, is_initial=False):
    """Process data fetching results and update global data"""
    for result in results:
        # Handle case where result is not a dictionary
        if not isinstance(result, dict):
            print(f"Warning: Expected dictionary result but got {type(result)}: {result}")
            continue

        strat_name = result.get('strategy_name')
        if not strat_name:
            print(f"Warning: Result missing strategy_name: {result}")
            continue

        df = result.get('df')
        if df is None:
            print(f"Warning: Result missing df for {strat_name}")
            continue

        if is_initial:
            # For initial load, simply set the data
            if not df.empty:
                gn_data[strat_name] = df
        else:
            # For regular updates, handle concatenation with existing data
            if not gn_data[strat_name].empty and not df.empty:
                gn_data[strat_name] = pd.concat([gn_data[strat_name], df]).drop_duplicates(subset=['t']).reset_index(
                    drop=True)
            elif not df.empty:
                gn_data[strat_name] = df


def run_scheduled_execution():
    """Run scheduled execution of trading strategies"""
    print('Run all strategies at', datetime.datetime.now().strftime("%Y-%m-%d %H:%M"))
    start_time = time.time()

    # Ensure exchange credentials are valid before starting
    if selected_exchange == "OKX":
        ensure_exchange_credentials()

    # Limit processes for OKX to avoid overwhelming rate limits
    processes = min(len(config['STRATEGIES']),
                    1 if selected_exchange == "OKX" else mp.cpu_count())

    max_retries = 3
    for attempt in range(max_retries):
        try:
            # Add better error handling for multiprocessing
            try:
                # Prepare strategies with global variables for multiprocessing
                strategies_with_globals = [prepare_strategy_with_globals(strategy) for strategy in config['STRATEGIES']]

                with mp.Pool(processes=processes) as pool:
                    results = pool.map(fetch_data_mp, strategies_with_globals)
            except (BrokenPipeError, OSError) as mp_error:
                print(f"Multiprocessing error: {mp_error}")
                print("Falling back to sequential processing...")
                # Fallback to sequential processing
                results = []
                for strategy in config['STRATEGIES']:
                    try:
                        strategy_with_globals = prepare_strategy_with_globals(strategy)
                        result = fetch_data_mp(strategy_with_globals)
                        results.append(result)
                    except Exception as e:
                        print(f"Error processing strategy {strategy['name']}: {e}")
                        # Create empty result for failed strategy
                        results.append({
                            "strategy_name": strategy['name'],
                            "df": pd.DataFrame(columns=['t', 'value', 'price'])
                        })

            process_data_results(results)

            # Get the signal and check if symbol is in max_positions.csv
            signal = calculate_position()
            _, _, _, symbol_found = check_max_positions_updated(force_reload=True)

            # Run calculations and execute trades if symbol is found
            execute_trade(signal, execute_orders=symbol_found)

            elapsed_time = time.time() - start_time
            print(f"Execution completed in {elapsed_time:.2f} seconds")
            execution_msg = f"✅ {SYMBOL} run completed in {elapsed_time:.2f} seconds"
            time.sleep(1)
            gc.collect()  # Explicitly free unused memory after sleep every iteration
            send_tg(execution_msg)
            break  # Exit loop on success
        except Exception as e:
            error_str = str(e)
            elapsed_time = time.time() - start_time

            # Check if this is a rate limit error
            if "Too Many Requests" in error_str or "rate limit" in error_str.lower() or "10006" in error_str:
                if attempt < max_retries - 1:
                    # Calculate backoff time with jitter
                    backoff_time = exponential_backoff(attempt, base_delay=10)  # Use larger base delay
                    print(
                        f"Rate limit hit during scheduled execution. Retry {attempt + 1}/{max_retries} after {backoff_time:.2f}s delay")
                    time.sleep(backoff_time)
                else:
                    error_msg = f"Rate limit error in scheduled execution after {max_retries} attempts: {str(e)}"
                    print(error_msg)
                    print(f"Execution failed after {elapsed_time:.2f} seconds")
                    # Only send critical errors to Telegram
                    send_tg(f"⚠️ {SYMBOL} scheduled run hit persistent rate limits")
                    gc.collect()  # Free memory even on error
            else:
                error_msg = f"Error in scheduled execution: {str(e)}"
                print(error_msg)
                print(f"Execution failed after {elapsed_time:.2f} seconds")

                # Only send critical errors to Telegram
                if attempt == max_retries - 1:
                    send_tg(f"⚠️ {SYMBOL} scheduled run error: {str(e)}")

                # For non-rate limit errors, use a smaller backoff
                if attempt < max_retries - 1:
                    backoff_time = 3 * (2 ** attempt)
                    print(f"Retrying in {backoff_time} seconds...")
                    time.sleep(backoff_time)

                gc.collect()  # Free memory even on error


def check_rebalance_complete():
    """
    Check if the rebalance process has fully completed (100%).

    Rebalance.py creates a special flag file 'rebalance_complete.flag' when
    it reaches 100% completion. This ensures accurate synchronization between
    Rebalance.py and trading scripts, including for subsequent rebalance cycles.

    IMPORTANT: This function never assumes completion based on time or file stability.
    It STRICTLY requires the explicit completion flag from Rebalance.py.

    Returns:
        bool: True if the rebalance process is complete, False if it's still running
    """
    try:
        data_dir = ensure_data_directory()
        max_positions_file = os.path.join(data_dir, 'max_positions.csv')
        complete_flag_file = os.path.join(data_dir, 'rebalance_complete.flag')

        # First check if max_positions.csv exists
        if not os.path.exists(max_positions_file):
            # Only show the "not found" message once per minute
            current_time = time.time()
            if (not hasattr(check_rebalance_complete, "last_notfound_msg_time") or
                    current_time - check_rebalance_complete.last_notfound_msg_time >= 60):
                print("max_positions.csv file not found - rebalance not started or still in progress")
                check_rebalance_complete.last_notfound_msg_time = current_time
            return False

        # Get the modification time of max_positions.csv
        positions_time = os.path.getmtime(max_positions_file)

        # Check if this function has been called before and store or update a timestamp
        # for when we first saw this version of max_positions.csv
        if not hasattr(check_rebalance_complete,
                       "positions_mtime") or check_rebalance_complete.positions_mtime != positions_time:
            # We're seeing a new version of max_positions.csv
            check_rebalance_complete.positions_mtime = positions_time
            check_rebalance_complete.was_waiting = True  # We're now waiting for this rebalance cycle

            # Reset the completion notification flag when we detect a new max_positions.csv
            if hasattr(check_rebalance_complete, "completion_reported"):
                delattr(check_rebalance_complete, "completion_reported")

            print(f"Detected new max_positions.csv (modified at {datetime.datetime.fromtimestamp(positions_time)})")
            print(f"New rebalance cycle detected - waiting for explicit completion flag")

            # Reset any stored flag file info
            if hasattr(check_rebalance_complete, "flag_cycle_id"):
                delattr(check_rebalance_complete, "flag_cycle_id")

        # Now check if the completion flag file exists - this is the ONLY way to determine completion
        if os.path.exists(complete_flag_file):
            # Get file creation/modification time
            flag_time = os.path.getmtime(complete_flag_file)

            # Read the contents of the flag file to get cycle information
            try:
                with open(complete_flag_file, 'r') as f:
                    flag_contents = f.read()

                # Extract cycle ID if present
                cycle_id = None
                if "Cycle ID:" in flag_contents:
                    for line in flag_contents.splitlines():
                        if line.startswith("Cycle ID:"):
                            cycle_id = line.split(":", 1)[1].strip()
                            break

                # Store the cycle ID if we haven't already
                if cycle_id and not hasattr(check_rebalance_complete, "flag_cycle_id"):
                    check_rebalance_complete.flag_cycle_id = cycle_id
                    print(f"Found rebalance completion flag with Cycle ID: {cycle_id}")

                # Check if flag file has max_positions timestamp and compare
                max_pos_in_flag = None
                if "max_positions.csv timestamp:" in flag_contents:
                    for line in flag_contents.splitlines():
                        if line.startswith("max_positions.csv timestamp:"):
                            try:
                                max_pos_in_flag = float(line.split(":", 1)[1].strip())
                                break
                            except (ValueError, TypeError):
                                pass

                # If the flag has a timestamp that matches our current max_positions.csv,
                # then we're definitely looking at the correct completion flag
                if max_pos_in_flag and abs(max_pos_in_flag - positions_time) < 1:  # Allow 1 second difference
                    # Check if we've already reported completion for this cycle
                    if not hasattr(check_rebalance_complete,
                                   "completion_reported") or not check_rebalance_complete.completion_reported:
                        # This flag specifically references our current max_positions.csv
                        print(f"✓ Rebalance completion flag confirms 100% completion of current rebalance cycle")

                        # If we were waiting, send a notification
                        if getattr(check_rebalance_complete, "was_waiting", True):
                            try:
                                # Check if the symbol exists in the file
                                df = pd.read_csv(max_positions_file)
                                if SYMBOL in df['symbol'].values:
                                    # Get the new max_pos value for the symbol
                                    max_pos = df.loc[df['symbol'] == SYMBOL, 'max_pos'].values[0]
                                    send_tg(
                                        f"⚡ {SYMBOL} rebalance 100% complete (Cycle ID: {cycle_id}). Trading activated with MAX_POS={max_pos:.6f}")
                            except Exception as e:
                                print(f"Error reading max positions after rebalance: {e}")

                            # Reset waiting state
                            check_rebalance_complete.was_waiting = False

                        # Mark that we've reported completion for this cycle
                        check_rebalance_complete.completion_reported = True

                    return True
            except Exception as e:
                print(f"Error reading rebalance completion flag: {e}")

            # If flag file is newer than max_positions.csv, rebalance is likely complete
            if flag_time >= positions_time:
                # Check if we've already reported completion for this cycle
                if not hasattr(check_rebalance_complete,
                               "completion_reported") or not check_rebalance_complete.completion_reported:
                    print(f"✓ Rebalance completion flag is newer than max_positions.csv - rebalance cycle is complete")

                    # If we were waiting, send a notification
                    if getattr(check_rebalance_complete, "was_waiting", True):
                        try:
                            # Check if the symbol exists in the file
                            df = pd.read_csv(max_positions_file)
                            if SYMBOL in df['symbol'].values:
                                # Get the new max_pos value for the symbol
                                max_pos = df.loc[df['symbol'] == SYMBOL, 'max_pos'].values[0]
                                cycle_id = getattr(check_rebalance_complete, "flag_cycle_id", "unknown")
                                send_tg(
                                    f"⚡ {SYMBOL} rebalance 100% complete (Cycle ID: {cycle_id}). Trading activated with MAX_POS={max_pos:.6f}")
                        except Exception as e:
                            print(f"Error reading max positions after rebalance: {e}")

                        # Reset waiting state
                        check_rebalance_complete.was_waiting = False

                    # Mark that we've reported completion for this cycle
                    check_rebalance_complete.completion_reported = True

                return True
            else:
                # Flag file exists but is older than max_positions file
                # This indicates a new rebalance started after the previous completion

                # Only show this message once per minute to reduce log spam
                current_time = time.time()
                if (not hasattr(check_rebalance_complete, "last_olderflag_msg_time") or
                        current_time - check_rebalance_complete.last_olderflag_msg_time >= 60):
                    age_difference = positions_time - flag_time
                    print(f"❌ Rebalance completion flag is {age_difference:.1f} seconds older than max_positions.csv")
                    print(f"A new rebalance cycle appears to be in progress - waiting for completion flag")
                    check_rebalance_complete.last_olderflag_msg_time = current_time

                return False

        # No completion flag found - must wait for explicit completion signal
        # Only show this message once per minute to reduce log spam
        current_time = time.time()
        if (not hasattr(check_rebalance_complete, "last_waiting_msg_time") or
                current_time - check_rebalance_complete.last_waiting_msg_time >= 60):
            print(
                f"❌ No rebalance completion flag found for current max_positions.csv - rebalance is still in progress")
            print(f"Waiting for Rebalance.py to create rebalance_complete.flag before enabling trading")
            check_rebalance_complete.last_waiting_msg_time = current_time

        # Important: Never assume completion based on time
        return False

    except Exception as e:
        print(f"Error checking rebalance completion: {e}")
        return False


def check_max_positions_updated(force_reload=False):
    """
    Check if max_positions.csv has been updated since last check
    and return the current max position for the symbol.

    Args:
        force_reload: Force reload the file even if it hasn't been updated

    Returns:
        tuple: (updated, current_max_pos, new_mtime, symbol_found)
            updated: Boolean indicating if file was updated
            current_max_pos: Current max position value for symbol
            new_mtime: Current modification time of the file
            symbol_found: Boolean indicating if SYMBOL was found in the file
    """
    global MAX_POS
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(current_dir, 'data')
    max_positions_file = os.path.join(data_dir, 'max_positions.csv')

    try:
        # If file doesn't exist, create it with defaults
        if not os.path.exists(max_positions_file):
            max_positions_file = ensure_max_positions_file()

        # Get file's modification time
        new_mtime = os.path.getmtime(max_positions_file)

        # Read current max position
        try:
            max_positions_df = pd.read_csv(max_positions_file)

            # Check if file is empty or has no valid columns
            if max_positions_df.empty or 'symbol' not in max_positions_df.columns:
                print(f"Max positions file is empty or missing required columns. Creating with defaults.")
                # Recreate the file with proper defaults
                max_positions_file = ensure_max_positions_file()
                max_positions_df = pd.read_csv(max_positions_file)

            symbol_row = max_positions_df.loc[max_positions_df['symbol'] == SYMBOL]

            # Track if symbol was found in the file
            symbol_found = len(symbol_row) > 0

            if not symbol_found:
                # Add this symbol to max_positions.csv if it doesn't exist
                print(f"Symbol {SYMBOL} not found in max_positions.csv. Adding with default values.")
                new_row = pd.DataFrame({
                    'symbol': [SYMBOL],
                    'strategy_count': [len(config['STRATEGIES'])],
                    'weight': [1.0],
                    'fund_allocation': [100000.0],
                    'max_pos': [1.0]
                })
                max_positions_df = pd.concat([max_positions_df, new_row], ignore_index=True)
                max_positions_df.to_csv(max_positions_file, index=False)
                current_max_pos = 1.0
                symbol_found = True  # Symbol now exists in the file
            else:
                current_max_pos = symbol_row['max_pos'].values[0]

                # Check if the rebalance process has fully completed
                # Only consider symbol "found" if rebalance is complete
                if symbol_found:
                    rebalance_complete = check_rebalance_complete()
                    if not rebalance_complete:
                        print(
                            f"Symbol {SYMBOL} found in max_positions.csv but rebalance process may still be in progress")
                        print(
                            f"Will delay trade execution until rebalance completes to avoid using incorrect max position")
                        symbol_found = False  # Override symbol_found to prevent trading until rebalance completes
        except pd.errors.EmptyDataError:
            # Handle empty CSV file
            print(f"Max positions file is empty. Recreating with defaults.")
            max_positions_file = ensure_max_positions_file()
            max_positions_df = pd.read_csv(max_positions_file)
            current_max_pos = 1.0  # Default value
            symbol_found = True  # Symbol should now exist in the new file

        # Always update MAX_POS to the current value
        if MAX_POS != current_max_pos:
            print(f"MAX_POS updated from {MAX_POS} to {current_max_pos}")
            MAX_POS = current_max_pos

        # No need to check last_mtime if force_reload is True
        if force_reload:
            return True, current_max_pos, new_mtime, symbol_found

        # Return True as updated if force_reload is True
        return force_reload, current_max_pos, new_mtime, symbol_found

    except Exception as e:
        print(f"Warning: Error while checking max positions: {e}")
        print("MAX_POS will remain unchanged")
        return False, MAX_POS, 0, False  # Return 0 as timestamp instead of None


def main():
    """Main function that initializes and runs the trading script"""
    # Try to initialize with retries
    init_retries = 3
    for attempt in range(init_retries):
        try:
            initialize_globals()
            break
        except Exception as e:
            error_str = str(e)

            # Check if this is a rate limit error
            if "Too Many Requests" in error_str or "rate limit" in error_str.lower() or "10006" in error_str:
                if attempt < init_retries - 1:
                    backoff_time = 30 + exponential_backoff(attempt,
                                                            base_delay=30)  # Use longer delay for initialization
                    print(
                        f"Rate limit hit during initialization. Retry {attempt + 1}/{init_retries} after {backoff_time:.2f}s delay")
                    time.sleep(backoff_time)
                else:
                    print(f"Failed to initialize after {init_retries} attempts due to rate limits.")
                    print("Will proceed with limited functionality.")
                    # Try to continue with basic initialization
                    try:
                        # Minimal initialization to keep the script running
                        current_dir = os.path.dirname(os.path.abspath(__file__))
                        key_file = os.path.join(current_dir, 'config', 'key.yaml')
                        config_file = os.path.join(current_dir, 'config', 'config_SUI.yaml')

                        with open(key_file) as f:
                            keys = yaml.safe_load(f)
                        with open(config_file) as f:
                            config = yaml.safe_load(f)

                        global SYMBOL, MAX_POS, SHIFT, RUN_FREQ
                        SYMBOL = config['ASSET'].get('symbol', '')
                        MAX_POS = 1.0  # Default
                        SHIFT = int(keys.get('SHIFT', 0))
                        RUN_FREQ = config.get('RUN_FREQ', 10)
                    except Exception as init_e:
                        print(f"Critical initialization error: {init_e}")
                        print("Cannot continue without basic configuration. Exiting.")
                        return
            else:
                # For other errors, retry with standard backoff
                if attempt < init_retries - 1:
                    backoff_time = 5 * (2 ** attempt)
                    print(f"Error during initialization: {e}")
                    print(f"Retrying in {backoff_time} seconds...")
                    time.sleep(backoff_time)
                else:
                    print(f"Critical initialization error after {init_retries} attempts: {e}")
                    print("Cannot continue. Exiting.")
                    return

    # Setup multiprocessing with better error handling
    try:
        # Use 'spawn' method for better stability on macOS
        mp.set_start_method('spawn', force=True)
    except RuntimeError:
        # If start method is already set, check what it is
        current_method = mp.get_start_method()
        print(f"Multiprocessing start method already set to: {current_method}")
        # If it's 'fork', try to change it to 'spawn' for better stability
        if current_method == 'fork':
            try:
                mp.set_start_method('spawn', force=True)
                print("Changed multiprocessing method from 'fork' to 'spawn' for better stability")
            except RuntimeError:
                print("Could not change multiprocessing method, continuing with current method")

    print('Start trading', SYMBOL, '.....')

    # Check and validate strategy_performance.csv at startup
    try:
        data_dir = ensure_data_directory()
        performance_file = os.path.join(data_dir, 'strategy_performance.csv')

        if os.path.exists(performance_file):
            print("Validating strategy_performance.csv at startup...")
            if check_and_repair_weight_ratios(performance_file):
                print("✓ Strategy performance file validation passed")
            else:
                print("⚠️ Strategy performance file validation failed - automatic repair attempted")
        else:
            print("Strategy performance file not found - will be created by Rebalance.py")
    except Exception as e:
        print(f"Error during startup validation of strategy_performance.csv: {e}")

    # Check if the symbol is in max_positions.csv
    try:
        _, _, last_mtime, symbol_found = check_max_positions_updated(force_reload=True)
        if last_mtime > 0:
            print(f"Initial MAX_POS: {MAX_POS}, last modified: {datetime.datetime.fromtimestamp(last_mtime)}")
        else:
            print(f"Initial MAX_POS: {MAX_POS}")

        if symbol_found:
            print(f"Symbol {SYMBOL} found in max_positions.csv - Will execute trades immediately")
        else:
            print(f"Symbol {SYMBOL} not found in max_positions.csv - Waiting for symbol to be added")
    except Exception as e:
        print(f"Error checking max positions: {e}")
        print("Will continue with default MAX_POS")
        symbol_found = False

    # No need to store initial weights as we'll reload for each calculation
    print("Strategy weights will be freshly loaded before each calculation")

    # If using OKX, add an initial delay before starting to ensure healthy connection
    if selected_exchange == "OKX":
        print("OKX exchange detected - adding initial stabilization delay before starting")
        time.sleep(5)

    # Run initial execution at startup
    try:
        run_initial_execution()
    except Exception as error:
        error_message = f"🚨 {SYMBOL} Initial execution error: {error}"
        print(error_message)
        try:
            send_tg(error_message)
        except:
            print("Could not send error notification")
        # Don't exit the program, continue with the main loop
        time.sleep(5)  # Add a delay before continuing

    # Initialize signal for storing calculated position
    last_signal = None

    # Track consecutive errors for exponential backoff
    consecutive_errors = 0
    rate_limit_errors = 0

    # Continue with regular scheduled runs
    while True:
        try:
            # Check if we have a symbol match in max_positions.csv
            try:
                _, _, new_mtime, symbol_found = check_max_positions_updated(force_reload=True)
            except Exception as e:
                print(f"Error checking max positions: {e}")
                # Continue with previous symbol_found value

            # Run regularly scheduled operations
            if datetime.datetime.now().second == 0:
                print('Time:', datetime.datetime.now().strftime("%Y-%m-%d %H:%M"))

                # Periodic validation of strategy_performance.csv (every hour)
                if datetime.datetime.now().minute == 0:
                    try:
                        data_dir = ensure_data_directory()
                        performance_file = os.path.join(data_dir, 'strategy_performance.csv')
                        if os.path.exists(performance_file):
                            check_and_repair_weight_ratios(performance_file)
                    except Exception as validation_error:
                        print(f"Error in periodic validation: {validation_error}")

                if datetime.datetime.now().minute % RUN_FREQ == 0:
                    try:
                        run_scheduled_execution()
                        # No need to store the signal since we execute immediately

                        # Send a notification if symbol status has changed
                        if symbol_found and last_signal is not None:
                            send_tg(
                                f"✅ {SYMBOL} is now active in max_positions.csv - trades will execute automatically")

                        # Reset consecutive errors counter on successful execution
                        consecutive_errors = 0
                        rate_limit_errors = 0
                    except Exception as sched_error:
                        print(f"Error in scheduled execution: {sched_error}")
                        # Don't exit, just continue to the next cycle

        except Exception as error:
            error_str = str(error)

            # Handle rate limit errors separately with a dedicated counter
            if "Too Many Requests" in error_str or "rate limit" in error_str.lower() or "10006" in error_str:
                rate_limit_errors += 1

                # Calculate backoff delay based on rate limit errors
                backoff_delay = exponential_backoff(min(rate_limit_errors, 5))

                print(f"Rate limit error: {error}")
                print(
                    f"Encountered {rate_limit_errors} rate limit errors. Backing off for {backoff_delay:.2f} seconds.")

                # Only send notification for persistent rate limit issues
                if rate_limit_errors >= 5:
                    if rate_limit_errors % 5 == 0:  # Only send every 5th occurrence to avoid spam
                        try:
                            error_message = f"⚠️ {SYMBOL} Rate limiting issues ({rate_limit_errors}): {error}"
                            send_tg(error_message)
                        except:
                            print("Could not send rate limit notification")

                # Wait based on exponential backoff before continuing
                time.sleep(backoff_delay)
            else:
                # For non-rate-limit errors
                consecutive_errors += 1

                # Calculate backoff delay for regular errors
                backoff_delay = min(2 ** consecutive_errors, 60)  # Cap at 60 seconds

                error_message = f"🚨 {SYMBOL} Error: {error}"
                print(error_message)
                print(f"Encountered {consecutive_errors} consecutive errors. Backing off for {backoff_delay} seconds.")

                # Only send Telegram notification for serious errors
                if consecutive_errors >= 3:
                    try:
                        error_message = f"🚨🚨🚨 {SYMBOL} Multiple errors ({consecutive_errors}): {error}"
                        send_tg(error_message)
                    except:
                        print("Could not send error notification")

                # Wait based on exponential backoff before continuing
                time.sleep(backoff_delay)

            gc.collect()  # Explicitly free unused memory

        # Always add a sleep between iterations to avoid tight loops
        time.sleep(1)
        gc.collect()  # Explicitly free unused memory after sleep every iteration


# ===== Enhanced Exchange API Functions =====

def handle_okx_request(func_name, *args, max_retries=OKX_MAX_RETRIES, **kwargs):
    """
    Special handler for OKX API requests with improved error handling.

    Args:
        func_name: Function name to call on the EXCHANGE object
        *args: Arguments to pass to the function
        max_retries: Maximum number of retry attempts
        **kwargs: Keyword arguments to pass to the function

    Returns:
        The result of the function call

    Raises:
        Exception: If all retry attempts fail
    """
    global EXCHANGE

    if not hasattr(EXCHANGE, func_name):
        raise AttributeError(f"Function {func_name} not found in exchange object")

    func = getattr(EXCHANGE, func_name)
    last_exception = None

    for attempt in range(max_retries):
        try:
            result = func(*args, **kwargs)

            # Success - add a small delay to help with rate limiting
            if attempt > 0:
                # Add a small delay after a successful retry to help avoid further rate limits
                time.sleep(0.5)

            return result

        except Exception as e:
            last_exception = e
            error_msg = str(e)

            # Check if this is a rate limit error
            if "Too Many Requests" in error_msg or "rate limit" in error_msg.lower():
                # Calculate delay using exponential backoff
                delay = exponential_backoff(attempt, base_delay=OKX_BASE_DELAY)
                print(f"OKX rate limit hit on {func_name}. Retry {attempt + 1}/{max_retries} after {delay:.2f}s delay")
                time.sleep(delay)

            # Check for authentication errors
            elif "Invalid API key" in error_msg or "signature" in error_msg.lower() or "auth" in error_msg.lower():
                # Try to reinitialize the exchange for authentication errors
                print(f"Authentication error in {func_name}: {error_msg}")
                try:
                    # Try to refresh exchange connection
                    if ensure_exchange_credentials():
                        print("Refreshed OKX credentials, retrying request")
                        # Wait a bit before retry
                        time.sleep(2)
                    else:
                        print("Failed to refresh OKX credentials")
                        # Wait longer before retry for auth failures
                        time.sleep(5)
                except Exception as auth_e:
                    print(f"Error refreshing credentials: {auth_e}")
                    time.sleep(5)

            # General network or timeout errors
            elif "timeout" in error_msg.lower() or "connection" in error_msg.lower():
                delay = exponential_backoff(attempt, base_delay=1.0)  # Use smaller base delay for network issues
                print(
                    f"Network error in {func_name}: {error_msg}. Retry {attempt + 1}/{max_retries} after {delay:.2f}s delay")
                time.sleep(delay)

            # Other unknown errors
            else:
                # For other errors, use a smaller delay
                delay = 1 + attempt  # Simple linear backoff for other errors
                print(f"API error in {func_name}: {error_msg}. Retry {attempt + 1}/{max_retries} after {delay}s delay")
                time.sleep(delay)

    # If we've exhausted all retries, raise the last exception
    print(f"Failed {func_name} after {max_retries} retry attempts")
    raise last_exception


def get_timeframe_milliseconds(timeframe):
    """Convert a timeframe string to milliseconds."""
    timeframe_map = {
        '1m': 60 * 1000,
        '3m': 3 * 60 * 1000,
        '5m': 5 * 60 * 1000,
        '15m': 15 * 60 * 1000,
        '30m': 30 * 60 * 1000,
        '1h': 60 * 60 * 1000,
        '2h': 2 * 60 * 60 * 1000,
        '4h': 4 * 60 * 60 * 1000,
        '6h': 6 * 60 * 60 * 1000,
        '12h': 12 * 60 * 60 * 1000,
        '1d': 24 * 60 * 60 * 1000,
        '1w': 7 * 24 * 60 * 60 * 1000,
    }
    return timeframe_map.get(timeframe, 60 * 1000)  # Default to 1m if not found


def load_strategy_weights():
    """
    Load strategy weights from strategy_performance.csv with validation and backup protection.
    Returns a dictionary with strategy names as keys and weight_ratio as values.
    If the file doesn't exist or is empty, returns None.
    """
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        data_dir = os.path.join(current_dir, 'data')
        performance_file = os.path.join(data_dir, 'strategy_performance.csv')

        # If file doesn't exist, create it with defaults
        if not os.path.exists(performance_file):
            performance_file = ensure_strategy_performance_file()

        # Check and repair weight_ratios before loading
        if not check_and_repair_weight_ratios(performance_file):
            print(f"Warning: Weight ratio integrity check failed for {performance_file}")

        # Read the strategy performance data
        performance_df = pd.read_csv(performance_file)

        # Check if weight_ratio column exists and add it if not
        if 'weight_ratio' not in performance_df.columns:
            print("Weight ratio column not found in strategy performance file. Adding it with empty values.")
            # Create backup before modifying
            create_strategy_performance_backup(performance_file)
            # Add weight_ratio column with empty strings
            performance_df['weight_ratio'] = [''] * len(performance_df)
            performance_df.to_csv(performance_file, index=False)

        # Create a dictionary of strategy_name to weight_ratio
        weights = {row['strategy_name']: row['weight_ratio'] for _, row in performance_df.iterrows()}

        # Check for missing strategies and add them to the file if needed
        missing_strategies = []
        for strategy in config['STRATEGIES']:
            if strategy['name'] not in weights:
                missing_strategies.append(strategy['name'])

        if missing_strategies:
            print(f"Adding missing strategies to performance file: {', '.join(missing_strategies)}")

            # Create backup before modifying
            create_strategy_performance_backup(performance_file)

            # Store existing weight_ratio values before adding new rows
            existing_weight_ratios = {row['strategy_name']: row['weight_ratio'] for _, row in performance_df.iterrows()}

            # Create rows for missing strategies with empty weight_ratio
            for strategy_name in missing_strategies:
                new_row = pd.DataFrame({
                    'strategy_name': [strategy_name],
                    'SR': [1.0],
                    'AR': [0.2],
                    'MDD': [0.1],
                    'CR': [2.0],
                    'POS': [0.0],
                    'weight_ratio': [''],  # Empty string for weight_ratio
                    'timestamp': [datetime.datetime.now()]
                })
                performance_df = pd.concat([performance_df, new_row], ignore_index=True)
                weights[strategy_name] = ''

            # Restore existing weight_ratio values to prevent corruption
            for idx, row in performance_df.iterrows():
                strategy_name = row['strategy_name']
                if strategy_name in existing_weight_ratios:
                    performance_df.at[idx, 'weight_ratio'] = existing_weight_ratios[strategy_name]

            # Validate before saving
            is_valid, corrupted_strategies, total_strategies = validate_weight_ratios(performance_df)
            if not is_valid:
                print(f"Error: Weight ratios became corrupted while adding missing strategies: {corrupted_strategies}")
                # Try to restore from backup
                backup_dir = os.path.join(data_dir, 'backups')
                valid_backup = find_latest_valid_backup(backup_dir)
                if valid_backup:
                    print("Restoring from backup due to corruption...")
                    restore_from_backup(performance_file, valid_backup)
                    # Re-read the restored data
                    performance_df = pd.read_csv(performance_file)
                    weights = {row['strategy_name']: row['weight_ratio'] for _, row in performance_df.iterrows()}
                else:
                    print("No valid backup found for restoration")
            else:
                # Save updated file only if validation passed
                performance_df.to_csv(performance_file, index=False)

        # Final validation of loaded weights
        final_is_valid, final_corrupted, final_total = validate_weight_ratios(performance_df)
        if not final_is_valid:
            print(f"Warning: Detected {len(final_corrupted)} corrupted weight_ratios after loading: {final_corrupted}")
            # Send alert about corruption
            try:
                send_tg(
                    f"⚠️ WARNING: {SYMBOL} detected corrupted weight_ratios during loading: {len(final_corrupted)}/{final_total} strategies affected")
            except:
                print("Could not send corruption alert via Telegram")

        # Convert numeric weight ratios for calculation
        # Check if all weight_ratio values are numeric
        all_weights_numeric = True
        for weight in weights.values():
            try:
                if weight == '' or pd.isna(weight):
                    all_weights_numeric = False
                    break
                float(weight)  # Try to convert to float
            except (ValueError, TypeError):
                all_weights_numeric = False
                break

        if all_weights_numeric:
            # Convert all weights to float for calculation
            weights = {k: float(v) for k, v in weights.items()}
            return weights
        else:
            # If any weight_ratio is empty or non-numeric, inform but return the weights dict
            print("Some weight_ratio values are empty or non-numeric")
            return weights

    except Exception as e:
        print(f"Warning: Error loading strategy weights: {e}")
        print("Using empty weight_ratios as fallback")

        # Return empty strings as fallback
        return {s['name']: '' for s in config['STRATEGIES']}


def calculate_position():
    global signal_data, strategy_sr, has_run_backtesting, latest_strategy_positions
    pos_total = 0
    strategy_sr = {}  # Reset SR values
    has_run_backtesting = False  # Reset the flag at the start of each calculation

    # Always reload max_positions.csv before calculating position
    is_updated, _, _, symbol_found = check_max_positions_updated(force_reload=True)

    # Always get fresh strategy weights for each calculation
    strategy_weights = load_strategy_weights()

    # Filter strategy weights to include only strategies for this symbol
    symbol_strategy_weights = {}
    for name, weight in strategy_weights.items():
        if name in [s['name'] for s in config['STRATEGIES']]:
            try:
                if weight != '' and not pd.isna(weight):
                    symbol_strategy_weights[name] = float(weight)
                else:
                    # If weight is empty or NaN, use fallback weights
                    symbol_strategy_weights[name] = 0.0
            except (ValueError, TypeError):
                # Use fallback if weight is not numeric
                symbol_strategy_weights[name] = 0.0

    # Check if any valid weights were found
    valid_weights_found = sum(symbol_strategy_weights.values()) > 0

    # If no valid weights, use equal weighting for all strategies
    if not valid_weights_found:
        print("No valid weights found in strategy_performance.csv - using equal weighting")
        equal_weight = 1.0 / len(config['STRATEGIES']) if config['STRATEGIES'] else 0.0
        symbol_strategy_weights = {s['name']: equal_weight for s in config['STRATEGIES']}
    else:
        # Normalize existing weights to sum to 1.0
        total_weight = sum(symbol_strategy_weights.values())
        for name in symbol_strategy_weights:
            symbol_strategy_weights[name] = symbol_strategy_weights[name] / total_weight

    # Use normalized weights for calculation
    calc_weights = symbol_strategy_weights
    weights = symbol_strategy_weights
    print("Using strategy weights:")

    print("Strategy Weights:")
    for name, weight in weights.items():
        if name in [s['name'] for s in config['STRATEGIES']]:
            print(f"  {name}: {weight:.6f}")

    # Calculate weighted position
    weighted_pos_total = 0
    strategy_positions = {}

    for strategy in config['STRATEGIES']:
        strategy_name = strategy['name']
        pos = apply_strategy(gn_data[strategy_name], strategy)

        # Use calculation weights (always numeric)
        weight = calc_weights.get(strategy_name, 0)  # Default to 0 if no weight found
        weighted_contribution = pos * weight
        weighted_pos_total += weighted_contribution

        # Store original weight in positions dictionary
        orig_weight = weights.get(strategy_name, '')
        strategy_positions[strategy_name] = {
            'pos': pos,
            'weight': orig_weight,  # Store original weight
            'contribution': weighted_contribution
        }

    new_row = pd.DataFrame([[datetime.datetime.now(), weighted_pos_total]], columns=['dt', 'pos'])
    if signal_data.empty:
        signal_data = new_row
    else:
        signal_data = pd.concat([signal_data, new_row], ignore_index=True)

    # Convert weighted_pos_total from percentage (-1 to 1) to actual target position
    # This is where we need to ensure the full MAX_POS value is used for 100% signal
    print('Net position signal from strategies:', f"{weighted_pos_total:.4f}")

    # Store strategy positions in global variable instead of attaching to DataFrame
    latest_strategy_positions = strategy_positions

    # Print individual strategy contributions with detailed breakdown
    print("\n" + "=" * 80)
    print("📊 DETAILED POSITION CALCULATION BREAKDOWN")
    print("=" * 80)

    print("\n🔍 Strategy Contributions:")
    total_contribution = 0
    for name, data in strategy_positions.items():
        print(f"  {name}:")
        print(f"    • Raw Position Signal: {data['pos']}")
        print(f"    • Strategy Weight: {data['weight']:.6f}")
        print(f"    • Weighted Contribution: {data['pos']} × {data['weight']:.6f} = {data['contribution']:.6f}")
        total_contribution += data['contribution']

    print(f"\n📈 Signal Aggregation:")
    print(f"  • Total Signal = Sum of all contributions = {total_contribution:.6f}")
    print(f"  • Signal Range: -1.0 (100% short) to +1.0 (100% long)")
    print(
        f"  • Current Signal: {total_contribution:.6f} = {abs(total_contribution) * 100:.1f}% {'LONG' if total_contribution > 0 else 'SHORT' if total_contribution < 0 else 'NEUTRAL'}")

    # Calculate target position with detailed explanation
    target_position = MAX_POS * weighted_pos_total

    print(f"\n🎯 Target Position Calculation:")
    print(f"  • Formula: Target Position = MAX_POS × Signal")
    print(f"  • MAX_POS (from max_positions.csv): {MAX_POS:.6f}")
    print(f"  • Signal (weighted): {weighted_pos_total:.6f}")
    print(f"  • Target Position = {MAX_POS:.6f} × {weighted_pos_total:.6f} = {target_position:.6f}")

    if MAX_POS == 0.0:
        print(f"\n⚠️  WARNING: MAX_POS is 0.0!")
        print(f"  • This means NO TRADING will occur regardless of signals")
        print(f"  • Check max_positions.csv file - fund_allocation might be 0.0")
        print(f"  • Run Rebalance.py to recalculate position sizes")
    elif target_position == 0.0:
        print(f"\n💡 Target position is 0.0 because:")
        if weighted_pos_total == 0.0:
            print(f"  • Signal is neutral (0.0) - no directional bias")
        else:
            print(f"  • MAX_POS is 0.0 - trading is disabled")
    else:
        direction = "LONG" if target_position > 0 else "SHORT"
        print(f"\n✅ This will lead to target position: {target_position:.6f} ({direction})")

    print("=" * 80)

    # Send backtest results only if backtesting was actually run
    if has_run_backtesting:
        # Accumulate backtest results for sending once
        backtest_messages = []
        # Create table header with monospaced font
        backtest_messages.append(f"📈 {SYMBOL} Backtest Results:\n")
        backtest_messages.append("Sharpe Ratio\n-------------------")

        # Calculate sum of positions while building the message
        sum_of_positions = 0
        for strategy in config['STRATEGIES']:
            strategy_data = strategy_sr.get(strategy['name'], {'sr': 0, 'pos': 0})
            sr = strategy_data['sr']
            pos = strategy_data['pos']
            sum_of_positions += pos
            # Add the arrow direction based on position
            direction = "➡️1" if pos > 0 else "➡️-1" if pos < 0 else "➡️0"
            backtest_messages.append(f"{strategy['name']}:\t{sr}{direction}")

        backtest_messages.append("-------------------")
        # Add sum of positions at the end
        sum_pos_formatted = int(sum_of_positions) if sum_of_positions.is_integer() else sum_of_positions
        backtest_messages.append(f"Sum of Pos: {sum_pos_formatted}")

        combined_message = "\n".join(backtest_messages)
        send_tg(combined_message)  # Send once after all strategies

    return weighted_pos_total


def current_pos():
    global EXCHANGE  # Use the global EXCHANGE object

    max_retries = 5  # Increased from 3 to 5
    retry_delay = 1  # seconds

    # Ensure we have valid credentials
    ensure_exchange_credentials()

    for attempt in range(max_retries):
        try:
            if selected_exchange == "BYBIT":
                # Don't reinitialize EXCHANGE here, use the global one
                position = EXCHANGE.fetch_position(SYMBOL)
                if len(position) == 0:
                    return 0
                if position['info']['side'] == 'Buy':
                    return float(position['info']['size'])
                elif position['info']['side'] == 'Sell':
                    return -float(position['info']['size'])
            elif selected_exchange == "BINANCE":
                positions = EXCHANGE.fetch_account_positions()
                if len(positions) == 0:
                    return 0
                position = next((p for p in positions if p['info']['symbol'] == SYMBOL), None)
                if position:
                    return float(position['info']['positionAmt'] or 0)
            elif selected_exchange == "OKX":
                # Use fetch_positions to get all open positions for OKX
                positions = EXCHANGE.fetch_positions([SYMBOL])
                if len(positions) == 0:
                    return 0
                # OKX may have separate entries for long and short positions
                total_position = 0
                for position in positions:
                    size = float(position['contracts'] or 0)
                    if position['side'] == 'long':
                        total_position += size
                    elif position['side'] == 'short':
                        total_position -= size
                return total_position

            # Default return value if exchange not recognized
            return 0
        except Exception as e:
            error_str = str(e)

            # Check if this is a rate limit error
            if "Too Many Requests" in error_str or "rate limit" in error_str.lower() or "10006" in error_str:
                if attempt < max_retries - 1:
                    # Calculate backoff time with jitter
                    backoff_time = exponential_backoff(attempt)
                    print(
                        f"Rate limit hit when fetching position. Retry {attempt + 1}/{max_retries} after {backoff_time:.2f}s delay")
                    time.sleep(backoff_time)
                else:
                    print(f"Failed to fetch position after {max_retries} attempts due to rate limits")
                    return 0  # Return 0 as a fallback
            else:
                if attempt < max_retries - 1:
                    print(f"Attempt {attempt + 1} failed: {str(e)}. Retrying in {retry_delay} seconds...")
                    # Try to reinitialize credentials in case of auth error
                    ensure_exchange_credentials()
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    print(f"Failed to fetch position after {max_retries} attempts: {str(e)}")
                    return 0

    return 0


def log_trade_execution(symbol, action, status, bet_size=None, current_pos=None, target_pos=None,
                        price=None, order_id=None, error_reason=None, order_type=None):
    """
    Log trade execution details to CSV file for analysis.
    Creates files like: data/trade_log_ETHUSDT.csv, data/trade_log_SUIUSDT.csv

    CSV columns: timestamp, symbol, action, status, bet_size, current_pos, target_pos,
                price, order_id, order_type, error_reason

    Args:
        symbol (str): Trading symbol
        action (str): 'BUY', 'SELL', or 'NO_TRADE'
        status (str): 'SUCCESS', 'FAILED', 'SKIPPED'
        bet_size (float): Order size
        current_pos (float): Current position before trade
        target_pos (float): Target position
        price (float): Execution price
        order_id (str): Exchange order ID
        error_reason (str): Reason for failure
        order_type (str): 'market' or 'limit'
    """
    try:
        data_dir = ensure_data_directory()
        log_file = os.path.join(data_dir, f'trade_log_{symbol}.csv')

        # Create log entry
        log_entry = {
            'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'symbol': symbol,
            'action': action,
            'status': status,
            'bet_size': bet_size,
            'current_pos': current_pos,
            'target_pos': target_pos,
            'price': price,
            'order_id': order_id,
            'order_type': order_type,
            'error_reason': error_reason
        }

        # Check if file exists to determine if we need headers
        file_exists = os.path.exists(log_file)

        # Convert to DataFrame and append to CSV
        df = pd.DataFrame([log_entry])
        df.to_csv(log_file, mode='a', header=not file_exists, index=False)

    except Exception as e:
        print(f"Error logging trade execution: {e}")
        # Don't raise exception to avoid interrupting trade execution


def ensure_strategy_signals_directory():
    """Create 'strategy_signals' directory if it doesn't exist"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    signals_dir = os.path.join(current_dir, 'strategy_signals')
    if not os.path.exists(signals_dir):
        os.makedirs(signals_dir)
        print(f"Created strategy_signals directory: {signals_dir}")
    return signals_dir


def record_strategy_signals(timestamp, execute_price, weighted_total_pos, individual_strategy_positions, symbol):
    """
    Record individual strategy signals for each trade cycle to CSV file.

    Args:
        timestamp (str): Execution timestamp from the exchange
        execute_price (float): Actual execution price from the exchange
        weighted_total_pos (float): Final weighted position signal
        individual_strategy_positions (dict): Dictionary containing individual strategy positions
        symbol (str): Trading symbol
    """
    try:
        signals_dir = ensure_strategy_signals_directory()
        signals_file = os.path.join(signals_dir, f'strategy_signals_{symbol}.csv')

        # Create the data row
        row_data = {
            'timestamp': timestamp,
            'execute_price': execute_price,
            'weighted_total_pos': weighted_total_pos
        }

        # Add individual strategy positions
        for strategy_name, strategy_data in individual_strategy_positions.items():
            row_data[strategy_name] = strategy_data['pos']

        # Check if file exists to determine if we need headers
        file_exists = os.path.exists(signals_file)

        # If file doesn't exist, we need to create headers based on current strategies
        if not file_exists:
            # Create headers: timestamp, execute_price, weighted_total_pos, then all strategy names
            headers = ['timestamp', 'execute_price', 'weighted_total_pos']
            strategy_names = [strategy['name'] for strategy in config['STRATEGIES']]
            headers.extend(strategy_names)

            # Create empty DataFrame with headers
            df = pd.DataFrame(columns=headers)
            df.to_csv(signals_file, index=False)
            print(f"Created strategy signals file: {signals_file}")

        # Read existing file to maintain proper column order
        existing_df = pd.read_csv(signals_file)

        # Ensure all expected columns exist in the row_data
        for col in existing_df.columns:
            if col not in row_data:
                row_data[col] = 0.0  # Default value for missing strategies

        # Create DataFrame from row_data and append
        new_row_df = pd.DataFrame([row_data])

        # Reorder columns to match existing file
        new_row_df = new_row_df.reindex(columns=existing_df.columns, fill_value=0.0)

        # Append to existing file
        new_row_df.to_csv(signals_file, mode='a', header=False, index=False)

        print(f"✅ Recorded strategy signals to {signals_file}")
        print(f"   • Timestamp: {timestamp}")
        print(f"   • Execute Price: {execute_price}")
        print(f"   • Weighted Total: {weighted_total_pos}")
        print(f"   • Individual Strategies: {len(individual_strategy_positions)}")

    except Exception as e:
        print(f"❌ Error recording strategy signals: {e}")
        # Don't raise exception to avoid interrupting trade execution


def get_order_timestamp(order_result):
    """
    Extract the execution timestamp from order result.

    Args:
        order_result (dict): Order result from exchange

    Returns:
        str: Formatted timestamp string
    """
    try:
        # Try to get timestamp from order result
        if order_result and 'timestamp' in order_result:
            # Convert milliseconds to datetime
            timestamp_ms = order_result['timestamp']
            if timestamp_ms:
                dt = datetime.datetime.fromtimestamp(timestamp_ms / 1000)
                return dt.strftime("%Y-%m-%d %H:%M:%S")

        # Fallback to current time if no timestamp in order result
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    except Exception as e:
        print(f"Error extracting order timestamp: {e}")
        # Fallback to current time
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("Trading Ended!")